import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>etProvider } from 'react-helmet-async';

import { GoogleOAuthProvider } from '@react-oauth/google';
import App from './App';
import * as amplitude from '@amplitude/analytics-browser';
import './index.css';
import userflow from 'userflow.js'
import { setupChunkLoadingErrorHandler } from '@/utils/chunkLoadingErrorHandler';
import ChunkLoadErrorBoundary from '@/components/ChunkLoadErrorBoundary';
import mixpanel from "mixpanel-browser";
import { PostHogProvider } from 'posthog-js/react'
import { initClarity } from '@/utils/clarity';

const root = ReactDOM.createRoot(document.getElementById('root'));
const helmetContext = {};

const params = {};
const queryString = window.location.search.substring(1);
const pairs = queryString.split('&');

pairs.forEach((pair) => {
  const [key, value] = pair.split('=');
  if (key) {
    params[decodeURIComponent(key)] = decodeURIComponent(value || '');
  }
});

userflow.init('*****************************') // Your token for Production

amplitude.init(import.meta.env.VITE_APP_AMPLITUDE_API_KEY, {
  deviceId: params.deviceId || undefined,
});

// Setup global chunk loading error handler only in production
if (window.location.hostname !== 'localhost') {
  setupChunkLoadingErrorHandler();
}

const options = {
  api_host: 'https://eu.i.posthog.com/',
  defaults: '2025-05-24',
}

// Near entry of your product, init Mixpanel
mixpanel.init("e9a77a7d119d8fd779c0996b6f09252b", {
  debug: true,
  track_pageview: true,
  persistence: "localStorage",
});

// Initialize Microsoft Clarity
initClarity();

root.render(
  // <React.StrictMode>
  <ChunkLoadErrorBoundary>
    <GoogleOAuthProvider clientId={import.meta.env.VITE_APP_GOOGLE_CLIENT_ID}>
      <HelmetProvider context={helmetContext}>
      <PostHogProvider apiKey={'phc_hldhufPXh807WZGAFY4sm9RQy7iOlnYj9kAVR6felLH'} options={options}>
        <App />
        </PostHogProvider>
      </HelmetProvider>
    </GoogleOAuthProvider>
  </ChunkLoadErrorBoundary>
  // </React.StrictMode>
);

import React, { useCallback, useEffect, useRef, useState } from 'react';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import VoicePreview from '@/components/Voice/VoicePreview';
import NewChatListMessages from '@/components/Chatbot/ChatListMessages/NewChatListMessages';
import DSelect from '@/components/Global/DSelect';
import * as conversationsService from '@/services/conversations.service';
import { getTextToVoiceOptionsWithApiKey } from '@/services/voice.service';
import { WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';
import VoiceSelector from '@/components/Voice/VoiceSelector';
import DButton from '@/components/Global/DButton';
import { updateChatbotCoreSettingsWithApiKey, updateChatbotPowerUpsWithApiKey } from '@/services/customization.service';
import  useToast  from '@/hooks/useToast';
import { getMessagesByConversationIdWithApiKey } from '@/services/message.service';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import { useUserStore } from '@/stores/user/userStore';
import { useNavigate, useSearchParams } from 'react-router-dom';

const CARD_WIDTH = 'w-[500px]';
const CARD_HEIGHT = 'h-[80vh]';
const ARABIC_DEMO_API_KEY = 'DANTE_PUBLIC_bf9130cabb0a8ddb717904ffa62ed9952f3d9b88b0d4940364e426727dfa9414';
const REQUIRED_ACCESS_TOKEN = 'tiger-group-x-dante-ai';

// AccessDeniedIcon component extracted from inline SVG
const AccessDeniedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
  </svg>
);

const arabicGreetings = [
  {
    id: "DPd861uv5p6zeVV94qOT", // Mo - MSA
    initial_message: "مرحبًا! كيف يمكنني مساعدتك اليوم؟"
  },
  {
    id: "g5jbuqCKrlENbXKVxm7G", // Ibrahim - MSA
    initial_message: "مرحبًا! كيف يمكنني مساعدتك اليوم؟"
  },
  {
    id: "rPNcQ53R703tTmtue1AT", // Mazen - MSA
    initial_message: "مرحبًا! كيف يمكنني مساعدتك اليوم؟"
  },
  {
    id: "IES4nrmZdUBHByLBde0P", // Haytham - Egyptian
    initial_message: "أهلاً! إزاي أقدر أساعدك النهارده؟"
  },
  {
    id: "LjKPkQHpXCsWoy7Pjq4U", // Alice - Egyptian
    initial_message: "أهلاً! إزاي أقدر أساعدك النهارده؟"
  },
  {
    id: "McVZB9hVxVSk3Equu8EH", // Audrey - Levantine
    initial_message: "أهلين! كيف فيني ساعدك اليوم؟"
  },
  {
    id: "ZpEtuMdTIlJXYXAhevhG", // Mia - Levantine
    initial_message: "أهلين! كيف فيني ساعدك اليوم؟"
  },
  {
    id: "DANw8bnAVbjDEHwZIoYa", // Ghavi - Gulf
    initial_message: "هلا! شلون أقدر أساعدك اليوم؟"
  },
  {
    id: "bPMKpgEe88vKSwusXTMU", // Bryan - Gulf
    initial_message: "هلا! شلون أقدر أساعدك اليوم؟"
  },
  {
    id: "5lXEHh42xcasVuJofypc", // Radar - Moroccan
    initial_message: "السلام! كيفاش نقدر نعاونك اليوم؟"
  },
  {
    id: "OfGMGmhShO8iL9jCkXy8", // Ghizlane - Moroccan
    initial_message: "السلام! كيفاش نقدر نعاونك اليوم؟"
  },
  {
    id: "A9ATTqUUQ6GHu0coCz8t", // Hamid - Moroccan
    initial_message: "السلام! كيفاش نقدر نعاونك اليوم؟"
  }
];

const ArabicDemo = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [selects, setSelects] = useState(['', '', '']);
  const [messages, setMessages] = useState([]);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [voiceStarted, setVoiceStarted] = useState(false);
  const [aiVoiceOptions, setAiVoiceOptions] = useState([]);
  const [voiceError, setVoiceError] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);
  const [hasAccess, setHasAccess] = useState(false);
  const pollingRef = useRef();
  const [initialMessage, setInitialMessage] = useState('');

  // Check access token on component mount
  useEffect(() => {
    const token = searchParams.get('token');
    if (token === REQUIRED_ACCESS_TOKEN) {
      setHasAccess(true);
    } else {
      setHasAccess(false);
    }
  }, [searchParams]);

  const handleSelectChange = (idx, value) => {
    const newSelects = [...selects];
    newSelects[idx] = value;
    setSelects(newSelects);
  };

  const createConversation = useCallback(async () => {
    try {
      const res = await conversationsService.postSharedCreateConversation({
        kb_id: '************************************',
        token: '0d15ab6a-0719-4524-a7c1-601f63ce9e02'
      });
      if(res.status === 201){
        setCurrentConversation(res?.data?.id);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  }, []);

  const handleUpdateConfiguration = async () => {
    if(!selects[2]){
      setVoiceError(true);
      return;
    }
    await createConversation();
    try{
      const res = await updateChatbotPowerUpsWithApiKey('************************************', {
        ai_voice_agent_id: selects[2]
      }, ARABIC_DEMO_API_KEY);

      const coreSettings = await updateChatbotCoreSettingsWithApiKey('************************************', {
        initial_message: [{ content: initialMessage }]
      }, ARABIC_DEMO_API_KEY);

      if(coreSettings.status === 200){
        setSuccessMessage('Configuration updated successfully');
      }
    }catch(e){
      console.log('error', e)
    }
  }

  useEffect(() => {
    if (!currentConversation || !voiceStarted) return;
    let isMounted = true;
    const poll = async () => {
      try {
        const res = await getMessagesByConversationIdWithApiKey(currentConversation, ARABIC_DEMO_API_KEY);
        const msgs = res?.data?.results || res?.data?.data?.results || [];
        if (isMounted && Array.isArray(msgs) && msgs.length > 0) {
          const finalMsg = msgs.map(msg => transformMessageToBubbleMessage(msg)).flat();
          setMessages(finalMsg);
        }
      } catch (err) {}
    };
    poll();
    pollingRef.current = setInterval(poll, 1000);
    return () => {
      isMounted = false;
      if (pollingRef.current) clearInterval(pollingRef.current);
    };
  }, [currentConversation, voiceStarted]);

  useEffect(() => {
    const getTextToVoice = async () => {
      try {
        const res = await getTextToVoiceOptionsWithApiKey(null, ARABIC_DEMO_API_KEY);
        if(res.status === 200) {

          const processedVoices = res.data.results.map((voice) => {
            let provider = 'Other';

            if (voice.voice_type) {
              switch(voice.voice_type.toLowerCase()) {
                case 'openai':
                  provider = 'OpenAI';
                  break;
                case 'elevenlabs':
                  provider = 'ElevenLabs';
                  break;
                case 'cartesia':
                  provider = 'Cartesia';
                  break;
                default:
                  provider = 'Other';
              }
            }

            return {
              label: voice.name,
              value: voice.id,
              voice_value: voice.id,
              soundUrl: voice.preview_url,
              provider: provider,
              description: voice.description || '',
              voice_type: voice.voice_type,
              external_id: voice.external_id
            };
          });

          setAiVoiceOptions(processedVoices);
        }
      } catch (err) {
        console.error('Error fetching provider voices:', err);
        setAiVoiceOptions([]);
      }
    };

    getTextToVoice();
  }, []);

  // If no access, show access denied message
  if (!hasAccess) {
    return (
      <div className="min-h-screen w-full bg-gradient-to-br from-[#f8fafc] to-[#e0e7ef] flex flex-col">
        <div className="flex items-center p-8 pb-4">
          <DFullLogo size="lg" />
        </div>
        <div className="flex flex-1 items-center justify-center px-10 pb-12">
          <div className="flex flex-col gap-6 bg-white/90 rounded-3xl shadow-2xl p-8 items-center max-w-md text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AccessDeniedIcon />
            </div>
            <h2 className="text-2xl font-bold text-gray-800">Access Denied</h2>
            <p className="text-gray-600 leading-relaxed">
              You do not have access to this page, please contact{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 underline">
                <EMAIL>
              </a>{' '}
              if you think that this is a mistake.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-[#f8fafc] to-[#e0e7ef] flex flex-col">
      <div className="flex items-center justify-between p-8 pb-4">
        <DFullLogo size="lg" />
        <img
          src="https://www.tigergroup.ae/assets/web/tigergroup-logo.webp"
          alt="Tiger Group"
          className="h-8 w-auto object-contain"
          onError={(e) => {
            e.target.style.display = 'none';
          }}
        />
      </div>
      <div className="flex flex-1 items-center justify-center gap-8 px-10 pb-12">
        <div className={`flex flex-col gap-3 bg-white/90 rounded-3xl shadow-2xl p-4 items-center w-[600px] overflow-y-auto ${CARD_HEIGHT}`}>
          <h2 className="text-xl font-bold mb-6 text-gray-800 text-center">Choose voice</h2>
          <div className="flex flex-col gap-2 w-full">
            <p className='text-sm text-gray-500'>1. Select a voice</p>
            <p className='text-sm text-gray-500'>2. Click on the update configuration button</p>
            <p className='text-sm text-gray-500'>3. Click on the start button of voice preview to start the conversation</p>
            <p className='text-sm text-gray-500'>4. Track the conversation in the chat messages section</p>
            </div>
            <div className='flex flex-col gap-size1 w-full'>
              <VoiceSelector
                selectedVoice={selects[2]}
                onVoiceChange={(value, externalId) => {
                  handleSelectChange(2, value)
                  setInitialMessage(arabicGreetings.find(greeting => greeting.id === externalId)?.initial_message || '');
                  setVoiceError(false);
                }}
                voices={aiVoiceOptions}
                kb_id={'************************************'}
                place="arabic-demo"
              />
              <p className={`text-sm text-center ${voiceError ? 'text-red-500' : 'text-green-500'} h-4`}>{voiceError ? 'Please select a voice' : successMessage || ''}</p>
            </div>
          <DButton variant='dark' size='md' className='w-full mt-4' onClick={handleUpdateConfiguration}>
            Update
          </DButton>
        </div>
        <div className={`flex flex-col items-center justify-center bg-white/95 rounded-3xl shadow-2xl p-5 ${CARD_WIDTH} ${CARD_HEIGHT}`}>
          <h2 className="text-xl font-bold mb-6 text-gray-800">Voice Preview</h2>
          <VoicePreview
            chatbotId={'************************************'}
            voiceId={selects[2]}
            welcomeMessage={'مرحباً! كيف يمكنني مساعدتك اليوم؟'}
            authToken={'0d15ab6a-0719-4524-a7c1-601f63ce9e02'}
            conversationId={currentConversation}
            place="arabic-demo"
            hideCloseButton={true}
            onStart={() => {
              setVoiceStarted(true);
              setMessages([]);
            }}
            onStop={() => {
              setTimeout(() => {
                setVoiceStarted(false);
              }, 2000);
            }}
            isButtonDisabled={!currentConversation}
            endpointType={WS_ENDPOINT_TYPES.SHARED}
          />
        </div>
        <div className={`flex flex-col bg-white/90 rounded-3xl shadow-2xl p-5 ${CARD_WIDTH} ${CARD_HEIGHT}`}>
          <h2 className="text-xl font-bold mb-6 text-gray-800 text-center">Chat Messages</h2>
          <div className="flex-1 overflow-y-auto">
            <NewChatListMessages
              messages={messages}
              isPreviewMode={true}
              showDate={false}
              chatbot_profile_pic={null}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArabicDemo;

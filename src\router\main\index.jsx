// src/Routers.jsx

import React, { Suspense, lazy, useEffect } from 'react';
import { createBrowserRouter, RouterProvider, Outlet, useLocation } from 'react-router-dom';

// Store hooks
import useLayoutStore from '@/stores/layout/layoutStore';

// Layouts
import LayoutShell from '@/layouts/LayoutShell';
import LayoutLiveAgent from '@/layouts/LayoutLiveAgent';
import LayoutDemo from '@/layouts/LayoutDemo';
import LayoutPublic from '@/layouts/LayoutPublic';

// Route wrappers
import Entrypoint from './Entrypoint';
import PrivateRoute from './PrivateRoute';
import PublicRoute from './PublicRoute';

// Global loading & error
import DLoading from '@/components/DLoading';
import ErrorPage from '@/pages/Error';
import ChangePasswordPage from '@/pages/Auth/ChangePassword';
import PageViewTracker from '@/components/PageViewTracker';
import RouteChangeHandler from '@/components/RouteChangeHandler';
import LayoutWrapper from '@/layouts/LayoutWrapper';
import LayoutPublicNoPadding from '@/pages/SignUpCampaign/LayoutPublicNoPadding';

// ============ Lazy-Loaded Pages ============

// Auth
const LogIn = lazy(() => import('@/pages/LogIn'));
const ForgotPasswordPage = lazy(() => import('@/pages/Auth/ForgotPassword'));
const ResetPasswordPage = lazy(() => import('@/pages/Auth/ResetPassword'));
const SignUp = lazy(() => import('@/pages/SignUp'));
const SignUpCampaign = lazy(() => import('@/pages/SignUpCampaign'));
const AcceptTeamInvite = lazy(() =>
  import('@/pages/TeamManagement/AcceptTeamInvite')
);

// Home & Misc
const Home = lazy(() => import('@/pages/Home'));
const FirstTimePage = lazy(() => import('@/pages/FirstTimePage'));
const Onboarding = lazy(() => import('@/pages/Onboarding'));
const Upgrade = lazy(() => import('@/pages/Upgrade'));
const Success = lazy(() => import('@/pages/Stripe/Success'));
const Cancel = lazy(() => import('@/pages/Stripe/Cancel'));
const Subscribe = lazy(() => import('@/pages/Subscribe'));
const Plans = lazy(() => import('@/pages/Plans'));
const Membership = lazy(() => import('@/pages/Membership'));
const MembershipPage = lazy(() => import('@/pages/Membership'));
const Branding = lazy(() => import('@/pages/Branding'));
const Profile = lazy(() => import('@/pages/Profile'));

// Human Handover
const HumanHandover = lazy(() => import('@/pages/HumanHandover'));
const HumanHandoverInsights = lazy(() =>
  import('@/pages/HumanHandover/Insights')
);
const LiveAgentEntrypoint = lazy(() => import('./LiveAgentEntrypoint'));
const HumanHandoverQuickResponses = lazy(() =>
  import('@/pages/HumanHandover/QuickResponses')
);
const HumanHandoverSettings = lazy(() =>
  import('@/pages/HumanHandover/Settings')
);
const HumanHandoverConversation = lazy(() =>
  import('@/pages/HumanHandover/Conversation')
);
const HumanHandoverDashboard = lazy(() =>
  import('@/pages/HumanHandover/Dashboard')
);
const VerifyHumanHandoverEmail = lazy(() =>
  import('@/pages/HumanHandover/VerifyHumanHandoverEmail')
);

// Chatbot
const CreateChatbot = lazy(() => import('@/pages/CreateChatbot'));
const ChatbotDetails = lazy(() => import('@/pages/ChatbotDetails'));
const ChatbotDetailsOutlet = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotDetailsOutlet')
);
const ChatbotTabs = lazy(() => import('@/pages/ChatbotDetails/ChatbotTabs'));
const ChatbotInsights = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotInsights')
);
const ChatAllRecords = lazy(() =>
  import('@/pages/ChatbotDetails/ChatAllRecords')
);
const ChatRecordDeatil = lazy(() =>
  import('@/pages/ChatbotDetails/ChatRecordDeatil/ChatRecordDeatil')
);
const ChatbotIntegrations = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotIntegrations')
);
const WhatsappIntegration = lazy(() =>
  import('@/pages/ChatbotDetails/Integrations/WhatsappIntegration')
);
const WordPressIntegration = lazy(() =>
  import('@/pages/ChatbotDetails/Integrations/WordpressIntegration')
);
const IntercomIntegration = lazy(() =>
  import('@/pages/ChatbotDetails/Integrations/IntercomIntegration')
);
const ChatbotSafety = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotSafety')
);
const ChatbotKnowledge = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotKnowledge')
);
const ChatbotSetup = lazy(() => import('@/pages/ChatbotDetails/ChatbotSetup'));
const ChatbotStyling = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotStyling')
);
const ChatbotPowerups = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotPowerups')
);
const ChatbotRealtimeVoice = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotRealtimeVoice')
);
const ChatbotPersonality = lazy(() =>
  import('@/pages/ChatbotDetails/ChatbotPersonality')
);

// Team
const TeamManagement = lazy(() => import('@/pages/TeamManagement'));

// Voice
const Voice = lazy(() => import('@/pages/Voice'));
const CreateVoice = lazy(() => import('@/pages/CreateVoice'));
const PhoneNumbers = lazy(() => import('@/pages/Voice/PhoneNumbers'));
const PhoneNumberPurchase = lazy(() => import('@/pages/Voice/PhoneNumbers/Purchase'));
const PhoneNumberPurchaseSuccess = lazy(() => import('@/pages/Voice/PhoneNumbers/Purchase/PurchaseSuccess'));
const PhoneNumberPurchaseCancel = lazy(() => import('@/pages/Voice/PhoneNumbers/Purchase/PurchaseCancel'));
const EditVoice = lazy(() => import('@/pages/EditVoice'));
const VoiceConversations = lazy(() => import('@/pages/Voice/Conversations'));
const VoiceConversationDetails = lazy(() => import('@/pages/Voice/ConversationDetail'));
const VoiceDetailsOutlet = lazy(() => import('@/pages/Voice/VoiceDetailsOutlet'));
const VoiceInsights = lazy(() => import('@/pages/Voice/Insights'));
const AIVoiceDemo = lazy(() => import('@/pages/AIVoiceDemo'));
// Demo
const Demo = lazy(() => import('@/pages/Demo'));
const ArabicDemo = lazy(() => import('@/pages/ArabicDemo'));

// Admin
const AdminDashboard = lazy(() => import('@/pages/AdminDashboard'));

const router = createBrowserRouter([
  // ================= Public Routes =================
  {
    path: '/log-in',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<LogIn />} />
      </PublicRoute>
    ),
  },
  {
    path: '/accept-organization-invite',
    element: (
      <LayoutPublicNoPadding leftSide={<VerifyHumanHandoverEmail />} />
    ),
  },
  {
    path: '/sign-up',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<SignUp />} />
      </PublicRoute>
    ),
  },
  {
    path: '/new-sign-up',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<SignUpCampaign />} />
      </PublicRoute>
    ),
  },
  {
    path: '/forgot-password',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<ForgotPasswordPage />} />
      </PublicRoute>
    ),
  },
  {
    path: '/reset-password',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<ResetPasswordPage />} />
      </PublicRoute>
    ),
  },
  {
    path: '/request-token',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<div>RequestToken</div>} />
      </PublicRoute>
    ),
  },
  {
    path: '/verify-email',
    element: (
      <PublicRoute>
        <LayoutPublicNoPadding leftSide={<div>VerifyEmail</div>} />
      </PublicRoute>
    ),
  },
  {
    path: '/demo',
    element: (
      <>
        <LayoutDemo>
          <Demo />
        </LayoutDemo>
      </>
    ),
  },
  {
    path: '/',
    element: (
      <PrivateRoute>
        <Entrypoint />
      </PrivateRoute>
    ),
    errorElement: (
      <PrivateRoute>
        <ErrorPage />
      </PrivateRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LayoutShell>
            <Home />
          </LayoutShell>
        ),
      },
      {
        path: 'first-time-setup',
        element: (
          <LayoutWrapper>
            <FirstTimePage />
          </LayoutWrapper>
        ),
      },
      {
        path: 'onboarding',
        element: (
          <PrivateRoute>
            <Outlet />
          </PrivateRoute>
        ),
        children: [
          { path: 'create', element: <Onboarding /> },
          { path: 'upgrade', element: <Upgrade /> },
        ],
      },
      {
        path: 'chatbot',
        element: (
          <LayoutShell>
            <Outlet />
          </LayoutShell>
        ),
        children: [
          { path: 'create', element: <CreateChatbot /> },
          {
            path: ':id',
            element: <ChatbotDetailsOutlet />,
            children: [
              { index: true, element: <ChatbotDetails /> },
              { path: 'tabs', element: <ChatbotTabs /> },
              { path: 'insights', element: <ChatbotInsights /> },
              { path: 'insights/records', element: <ChatAllRecords /> },
              {
                path: 'insights/records/:recordId',
                element: <ChatRecordDeatil />,
              },
              { path: 'integrations', element: <ChatbotIntegrations /> },
              {
                path: 'integrations/whatsapp',
                element: <WhatsappIntegration />,
              },
              {
                path: 'integrations/intercom',
                element: <IntercomIntegration />,
              },
              {
                path: 'integrations/wordpress',
                element: <WordPressIntegration />,
              },
              { path: 'safety', element: <ChatbotSafety /> },
              { path: 'knowledge', element: <ChatbotKnowledge /> },
              { path: 'personality', element: <ChatbotPersonality /> },
              { path: 'setup', element: <ChatbotSetup /> },
              { path: 'styling', element: <ChatbotStyling /> },
              { path: 'powerups', element: <ChatbotPowerups /> },
              { path: 'realtime-voice', element: <ChatbotRealtimeVoice /> },
            ],
          },
        ],
      },
      {
        path: 'profile',
        element: (
          <LayoutShell>
            <Profile />
          </LayoutShell>
        ),
      },
      {
        path: 'membership',
        element: (
          <LayoutShell>
            <Membership />
          </LayoutShell>
        ),
      },
      {
        path: 'membership-page',
        element: (
          <LayoutShell>
            <MembershipPage />
          </LayoutShell>
        ),
      },
      {
        path: 'team-management',
        element: (
          <LayoutShell>
            <TeamManagement />
          </LayoutShell>
        ),
        children: [{ path: ':id', element: <TeamManagement /> }],
      },
      // Branding feature removed - Issue #1531
      // {
      //   path: 'branding',
      //   element: (
      //     <LayoutShell>
      //       <Branding />
      //     </LayoutShell>
      //   ),
      // },
      {
        path: 'voice',
        element: (
          <LayoutShell>
            <Outlet />
          </LayoutShell>
        ),
        children: [
          { index: true, element: <Voice /> },
          { path: 'create', element: <CreateVoice /> },
          { path: ':id/edit', element: <EditVoice /> },
          {
            path: ':id',
            element: <VoiceDetailsOutlet />,
            children: [
              { path: 'conversations', element: <VoiceConversations /> },
              { path: 'analytics', element: <VoiceInsights /> },
              { path: 'conversations/:conversationId', element: <VoiceConversationDetails /> },
            ]
          },
        ],
      },
      {
        path: 'phone-numbers',
        element: (
          <LayoutShell>
            <Outlet />
          </LayoutShell>
        ),
        children: [
          { index: true, element: <PhoneNumbers /> },
          { path: 'purchase', element: <PhoneNumberPurchase /> },
          { path: 'purchase/success', element: <PhoneNumberPurchaseSuccess /> },
          { path: 'purchase/cancel', element: <PhoneNumberPurchaseCancel /> },
        ],
      },
      {
        path: 'human-handover',
        element: (
          <LayoutShell>
            <HumanHandover />
          </LayoutShell>
        ),
      },
      {
        path: 'human-handover/:organization_id',
        element: (
          <LayoutLiveAgent>
            <LiveAgentEntrypoint />
          </LayoutLiveAgent>
        ),
        children: [
          { index: true, element: <HumanHandoverDashboard /> },
          { path: 'insights', element: <HumanHandoverInsights /> },
          { path: 'quick-responses', element: <HumanHandoverQuickResponses /> },
          { path: 'settings', element: <HumanHandoverSettings /> },
          { path: 'conversation/:conversation_id', element: <HumanHandoverConversation /> },
        ],
      },
      {
        path: 'subscribe',
        element: (
          <LayoutShell>
            <Subscribe />
          </LayoutShell>
        ),
      },
      {
        path: 'plans',
        element: (
          <LayoutShell>
            <Plans />
          </LayoutShell>
        ),
      },
      {
        path: 'admin/dashboard',
        element: (
          <LayoutShell>
            <AdminDashboard />
          </LayoutShell>
        ),
      },
      {
        path: 'accept-team-invite',
        element: <AcceptTeamInvite />,
      },
      {
        path: 'change-password',
        element: (
          <PrivateRoute>
            <Outlet />
          </PrivateRoute>
        ),
        children: [{ path: '', element: <ChangePasswordPage /> }],
      },
    ],
  },
  // Standalone AIVoiceDemo route - protected only by its own token system
  {
    path: '/ai-voice/demo',
    element: (
      <>
        <PageViewTracker />
        <RouteChangeHandler />
        <AIVoiceDemo />
      </>
    )
  },
  {
    path: '/stripe',
    element: (
      <PrivateRoute>
        <Outlet />
      </PrivateRoute>
    ),
    children: [
      {
        path: 'success',
        element: (
          <>
            <PageViewTracker />
            <RouteChangeHandler />
            <Success />
          </>
        )
      },
      {
        path: 'cancel',
        element: (
          <>
            <PageViewTracker />
            <RouteChangeHandler />
            <Cancel />
          </>
        )
      },
    ],
  },
  {
    path: '/tiger-group/demo',
    element: (
      <>
        <ArabicDemo />
      </>
    ),
  },
]);

export default function Routers() {
  return (
    <Suspense fallback={<DLoading show={true} />}>
      <RouterProvider router={router} />
    </Suspense>
  );
}

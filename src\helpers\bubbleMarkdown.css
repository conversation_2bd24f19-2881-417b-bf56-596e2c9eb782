.language-markdown {
    text-wrap: wrap !important;
    display: block !important;
    width: 100% !important;
    overflow-wrap: break-word !important;
    white-space: pre-wrap !important; 
    background: rgba(9, 8, 31, 1) !important;
    color: white !important;
    text-shadow: black 0px -0.1em 0.2em !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !important;
    font-size: 1em !important;
    text-align: left !important;
    line-height: 1.5 !important;
    word-spacing: normal !important;
    word-break: normal !important;
    tab-size: 4 !important;
    hyphens: none !important;
    padding: 1em !important;
    margin: 0.5em 0px !important;
    overflow: auto !important;
    border-radius: 0.5em !important;
    box-shadow: black 1px 1px 0.5em inset !important;
    border: none !important;
}

.language-markdown span {
    text-wrap: wrap !important;
}

.custom-syntax-highlighter {
    color: white !important;
    background: rgba(9, 8, 31, 1) !important;
    text-shadow: black 0px -0.1em 0.2em !important;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace !important;
    font-size: 1em !important;
    text-align: left !important;
    white-space: pre-wrap !important;
    word-spacing: normal !important;
    word-break: normal !important;
    overflow-wrap: normal !important;
    line-height: 1.5 !important;
    tab-size: 4 !important;
    hyphens: none !important;
    padding: 1em !important;
    margin: 0.5em 0px !important;
    overflow: auto !important;
    border-radius: 0.5em !important;
    box-shadow: black 1px 1px 0.5em inset !important;
    border: none !important;
}
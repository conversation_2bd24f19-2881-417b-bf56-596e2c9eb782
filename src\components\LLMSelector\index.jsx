import { useEffect, useState, useMemo } from 'react';
import { groupBy } from 'lodash';

import { iconToCompanyName, LLM_MODEL_DEFAULT } from '@/constants';
import loadIcon from '@/helpers/loadIcons';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import { getLLMModels } from '@/services/model.service';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import { updateChatbotLLMModel } from '@/services/chatbot.service';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';

import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import OpenAiLogoIcon from '../Global/Icons/OpenAiLogoIcon';
import useToast from '@/hooks/useToast';
import DSpinner from '../Global/DSpinner';
import { useUserStore } from '@/stores/user/userStore';
import { useParams } from 'react-router-dom';
import featureCheck, { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import SelectedIcon from '../Global/Icons/SelectedIcon';
import useModalStore from '@/stores/modal/modalStore';

const LLMSelector = () => {
  const { user } = useUserStore();
  const { openPlansModal } = useModalStore();
  const params = useParams();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [selectedLLM, setSelectedLLM] = useState(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  const setSelectedLLModel = useChatbotStore(
    (state) => state.setSelectedLLModel
  );
  const [icons, setIcons] = useState({});
  const [iconsLoaded, setIconsLoaded] = useState(false);

  const [modelsByCompany, setModelsByCompany] = useState({});

  const { data: models } = useDanteApi(getLLMModels, [], {}, {
    tier_type: user.tier_type,
  });

  const { addWarningToast } = useToast();

  // Memoized helper function to get the correct icon component for a model
  const getModelIcon = useMemo(() => {
    return (model) => {
      if (!model?.Logo) return OpenAiLogoIcon;
      return icons[model.Logo] || OpenAiLogoIcon;
    };
  }, [icons]);

  // Memoized helper function to create a model with the correct icon
  const createModelWithIcon = useMemo(() => {
    return (model) => {
      if (!model) return LLM_MODEL_DEFAULT;
      return {
        ...model,
        Icon: getModelIcon(model)
      };
    };
  }, [getModelIcon]);

  const handleChangeModel = async (model) => {

    if(model.value === 'cohere' && !featureCheck('cohere_model')) {
      return;
    }

    if(model.value.includes('claude') && !featureCheck('claude_model')) {
      return;
    }

    if (!model.available) {
      addWarningToast({
        message:
          'This model is not available for your plan. Please upgrade your plan to use this model.',
      });
      return;
    }

    // Create model with correct icon
    const modelWithIcon = createModelWithIcon(model);
    setSelectedLLM(modelWithIcon);
    setSelectedLLModel(modelWithIcon);

    try{
        await updateChatbotLLMModel({
            kb_id: params?.id,
            llmModel: model.value
        });
    } catch (e) {
        console.error('Failed to update chatbot LLM model', e);
    }
  };

  useEffect(() => {
    const loadModels = async () => {
      const groupedModels = groupBy(models, 'Logo');

      const iconComponents = {};

      for (const company of Object.keys(groupedModels)) {
        try {
          const iconCompany = await loadIcon(company);
          iconComponents[company] = iconCompany;
        } catch (e) {
          console.error(`Failed to load icon ${company}`, e);
        }
      }

      setIcons(iconComponents);
      setModelsByCompany(groupedModels);
      setIconsLoaded(true);
    };
    if (models?.length) {
      loadModels();
    }
  }, [models]);

  useEffect(() => {
    const model = selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT;
    const modelWithIcon = createModelWithIcon(model);
    setSelectedLLM(modelWithIcon);
  }, [selectedChatbot?.last_model_used, createModelWithIcon]);

  // Show loading state when models are being loaded or icons are not ready
  if (!models || models.length === 0 || !iconsLoaded) {
    return (
      <div className="flex items-center justify-center gap-size1 text-xs">
        <DSpinner style={{ height: 24, width: 24 }} />
      </div>
    );
  }

  // Ensure we always have a valid icon component
  const IconComponent = getModelIcon(selectedLLM);

  // Additional safety check - ensure selectedLLM has proper structure
  const safeSelectedLLM = selectedLLM || LLM_MODEL_DEFAULT;

  return (
    <Listbox
      value={safeSelectedLLM}
      onChange={handleChangeModel}
      className="group"
      as="div"
    >
      <ListboxButton className="flex items-center justify-between gap-size1 text-xs ">
        <div className="flex items-center justify-center h-full">
          <IconComponent className="size-4" />
        </div>
        <div className="flex items-center justify-center">
          {safeSelectedLLM.label}
        </div>
        <ChevronDownIcon
           className="transition-transform group pointer-events-none size-3 fill-black group-data-[open]:rotate-180"
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor={{
          to: 'top start',
          gap: 8,
        }}
        transition
        className="flex flex-col  gap-size2 py-size2 rounded-size2 border border-grey-10 bg-white origin-bottom transition duration-200 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 z-[999] !max-h-[500px] overflow-y-auto scrollbar"
      >
        {Object.keys(modelsByCompany)?.map((company) => {
          const Icon = icons[company];

          return (
            <div key={company} className="flex flex-col gap-size0">
                  <span className="text-xs font-medium text-grey-50 px-size2">
                {iconToCompanyName[company]}
              </span>
              {modelsByCompany[company].map((model) => {
                const isModelDisabled = !model.available ||
                  (model.value === 'cohere' && !checkFeatureAvailability('cohere_model', user.tier_type)) ||
                  (model.value.includes('claude') && !checkFeatureAvailability('claude_model', user.tier_type));

                return (
                  <ListboxOption
                    as="button"
                    key={model.value}
                    value={model}
                    className={`flex items-center gap-size1 text-sm hover:bg-grey-5 py-size0 px-size2 cursor-pointer data-[selected]:text-grey-50 ${isModelDisabled ? 'opacity-50' : ''}`}
                    onClick={(e) => {
                      if (isModelDisabled) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (model.value === 'cohere' && !checkFeatureAvailability('cohere_model', user.tier_type)) {
                          openPlansModal('cohere_model');
                        } else if (model.value.includes('claude') && !checkFeatureAvailability('claude_model', user.tier_type)) {
                          openPlansModal('claude_model');
                        }
                        return false;
                      }
                    }}
                  >
                      {Icon && <Icon className="size-4" />}
                      <div className='flex flex-col items-start flex-1'>
                        <span>{model.label}</span>
                       
                        <span className='text-xs leading-none text-grey-50'>
                          {model.credits} credits per response
                        </span>
                     
                      </div>
                      {selectedLLM.value === model.value && (
                        <div className="flex items-center justify-center size-4 rounded-full bg-brand-100">
                           <SelectedIcon />
                        </div>
                      )}
                  </ListboxOption>
                );
              })}
            </div>
          );
        })}
      </ListboxOptions>
    </Listbox>
  );
};

export default LLMSelector;

import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useGoogleLogin } from '@react-oauth/google';
import loginGoogleUseCase from '@/application/auth/loginGoogle';
import { useUserStore } from '@/stores/user/userStore';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import DLoading from '@/components/DLoading';
import useToast from '@/hooks/useToast';

// Import our new components
import LoginForm from './steps/LoginForm';
import LoginOTP from './steps/LoginOTP';
import ForgotPassword from '../Auth/ForgotPassword';

// Define our login steps
export const LoginSteps = {
  LOGIN_FORM: 'login_form',
  LOGIN_OTP: 'login_otp',
  FORGOT_PASSWORD: 'forgot_password',
};

const LogIn = () => {
  const [urlParams] = useSearchParams();
  const navigate = useNavigate();
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const { addErrorToast } = useToast();

  // State for managing the flow
  const [currentStep, setCurrentStep] = useState(LoginSteps.LOGIN_FORM);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Page loading state
  const [pageLoading, setPageLoading] = useState(true);

  /**
   * Handles Google OAuth login
   */
  const handleGoogleLogIn = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setLoading(true);
      setError('');
      try {
        const rewardful_referral = window.Rewardful?.referral;
        await loginGoogleUseCase({
          ...tokenResponse,
          rewardful_referral,
          isLanding: urlParams.get('is_landing_page'),
          kb_id: urlParams.get('kb_id'),
        });

        navigate('/');
      } catch (err) {
        console.error(err);
        // Handle backend errors with user-friendly messages
        if (err.response?.data?.detail) {
          const errorDetail = err.response.data.detail;

          // Show the exact backend error message
          addErrorToast({
            message: errorDetail,
          });
        } else {
          // For non-backend errors (network issues, etc.)
          addErrorToast({
            message: err.message || 'An error occurred during Google login',
          });
        }
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Login Failed:', error);
      addErrorToast({
        message: 'Google login failed. Please try again.',
      });
      setLoading(false);
    },
  });

  // Check if user has token in URL params
  useEffect(() => {
    if (urlParams.get('token')) {
      saveAuthDetail({
        access_token: urlParams.get('token'),
      });
      navigate('/');
    }
  }, [urlParams]);

  // Simulate page loading and set pageLoading to false when all resources are loaded
  useEffect(() => {
    // Wait for the document to be fully loaded
    const handleLoad = () => {
      // Add a small delay to ensure all resources are loaded and rendered
      setTimeout(() => {
        setPageLoading(false);
      }, 500);
    };

    // If document is already loaded, call handleLoad directly
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);

      // Fallback: set pageLoading to false after a maximum timeout (2 seconds)
      const timeoutId = setTimeout(() => {
        setPageLoading(false);
      }, 2000);

      return () => {
        window.removeEventListener('load', handleLoad);
        clearTimeout(timeoutId);
      };
    }
  }, []);

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case LoginSteps.LOGIN_FORM:
        return (
          <LoginForm
            email={email}
            setEmail={setEmail}
            password={password}
            setPassword={setPassword}
            handleGoogleLogIn={handleGoogleLogIn}
            goToOTP={() => setCurrentStep(LoginSteps.LOGIN_OTP)}
            goToForgotPassword={() =>
              setCurrentStep(LoginSteps.FORGOT_PASSWORD)
            }
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
      case LoginSteps.LOGIN_OTP:
        return (
          <LoginOTP
            email={email}
            password={password}
            otpCode={otpCode}
            setOtpCode={setOtpCode}
            goBack={() => setCurrentStep(LoginSteps.LOGIN_FORM)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
      case LoginSteps.FORGOT_PASSWORD:
        return (
          <ForgotPassword
            email={email}
            setEmail={setEmail}
            goBack={() => setCurrentStep(LoginSteps.LOGIN_FORM)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
      default:
        return (
          <LoginForm
            email={email}
            setEmail={setEmail}
            password={password}
            setPassword={setPassword}
            handleGoogleLogIn={handleGoogleLogIn}
            goToOTP={() => setCurrentStep(LoginSteps.LOGIN_OTP)}
            goToForgotPassword={() =>
              setCurrentStep(LoginSteps.FORGOT_PASSWORD)
            }
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
    }
  };

  if (pageLoading) {
    return <DLoading show={true} />;
  }

  return (
    <div
      className="relative w-full h-dvh overflow-y-auto overflow-x-hidden 1600px:overflow-hidden "
      data-testid="login-page"
    >
      {/* Background gradient */}
      <div className="absolute bottom-[-20%] right-[-20%] w-[120%] h-[80%] rounded-tl-full rounded-tr-full bg-gradient-radial from-purple-200 to-transparent opacity-20 pointer-events-none"></div>
      <div className="flex flex-col items-center justify-center min-h-full px-4 py-8">
        {/* Content with entry animation */}
        <div
          className="relative z-10 w-full max-w-md animate-fadeIn"
          data-testid="login-content"
        >
          <div className="flex justify-center mb-4 h-8" data-testid="login-logo">
            <DFullLogo />
          </div>
          {/* Wrapper for both main content and signup link with single animation */}
          <div className="animate-fadeInUp" data-testid="login-form-wrapper">
            {renderStep()}

            {/* Sign Up Link - always shown */}
            <div
              className="bg-gray-100 py-4 rounded-b-3xl text-center shadow-lg mx-2 md:mx-0"
              data-testid="signup-link-container"
            >
              <p className="text-sm text-gray-500">
                Don&apos;t have an account?{' '}
                <Link to="/sign-up" className="text-indigo-600 hover:underline">
                  Sign up
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogIn;

import WebSocketService from './WebSocketService';
import { WS_CLOSE_CODES } from './constants';

/**
 * HumanHandoverWebSocketService - A specialized WebSocket service for human handover functionality
 *
 * Implements the requirements for the human handover/live agent flow:
 * 1. User → Agent: Messages from user are forwarded in real time over WebSocket
 * 2. Agent → User: Messages from agent appear instantly in the Chat UI
 * 3. Lifecycle & Recovery:
 *    - Automatically authenticate with { auth_token, shared } on open
 *    - Retry on network errors/abnormal closes, up to 5 times, with a 1s back-off
 *    - Clean teardown when chat closes or agent resolves the conversation
 * 4. State Events:
 *    - human_handover_taken: Display "Agent joined" info bubble
 *    - human_handover_resolved: Display "Conversation marked resolved" info bubble and close socket
 */
class HumanHandoverWebSocketService extends WebSocketService {
  /**
   * Create a new HumanHandoverWebSocketService instance
   * @param {Object} config - Configuration options
   * @param {string} config.url - WebSocket URL to connect to
   * @param {Object} config.options - Additional options
   * @param {string} config.authToken - Authentication token
   * @param {boolean} config.isShared - Whether this is a shared connection (bubble/direct link/iframe)
   * @param {boolean} config.isInHumanHandoverApp - Whether this is in the human handover app
   * @param {Object} callbacks - Callback functions for WebSocket events
   * @param {Function} callbacks.onAgentJoined - Called when an agent joins the conversation
   * @param {Function} callbacks.onAgentResolved - Called when an agent resolves the conversation
   * @param {Function} callbacks.onMessageReceived - Called when a message is received
   * @param {Function} callbacks.onTypingStart - Called when someone starts typing
   * @param {Function} callbacks.onTypingStop - Called when someone stops typing
   */
  constructor(config, callbacks = {}) {
    // Set default options for human handover
    const options = {
      maxRetries: 5,
      retryDelayMs: 1000,
      connectionTimeoutMs: 5000,
      autoReconnect: true,
      ...config.options
    };

    // Create enhanced callbacks that handle human handover specific events
    const enhancedCallbacks = {
      onOpen: (event) => {
        // Send authentication after connection is established
        this.sendAuthentication();

        // Call the original onOpen callback if provided
        if (callbacks.onOpen) {
          callbacks.onOpen(event);
        }
      },
      onMessage: (event) => {
        try {
          const parsedData = JSON.parse(event.data);

          // Handle different message types
          if (parsedData.type === 'message') {
            // Message from agent or user
            if (callbacks.onMessageReceived) {
              callbacks.onMessageReceived(parsedData);
            }
          } else if (parsedData.type === 'human_handover_taken') {
            // Agent has joined the conversation
            if (callbacks.onAgentJoined) {
              callbacks.onAgentJoined(parsedData);
            }
          } else if (parsedData.type === 'human_handover_resolved') {
            // Agent has resolved the conversation
            if (callbacks.onAgentResolved) {
              callbacks.onAgentResolved(parsedData);
            }

            // Automatically disconnect when conversation is resolved
            this.disconnect(WS_CLOSE_CODES.NORMAL_CLOSURE, 'Conversation resolved');
          } else if (parsedData.type === 'typing_start') {
            // Someone started typing
            if (callbacks.onTypingStart) {
              callbacks.onTypingStart(parsedData);
            }
          } else if (parsedData.type === 'typing_stop') {
            // Someone stopped typing
            if (callbacks.onTypingStop) {
              callbacks.onTypingStop(parsedData);
            }
          }

          // Call the original onMessage callback if provided
          if (callbacks.onMessage) {
            callbacks.onMessage(event);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);

          // Call the original onMessage callback even if parsing fails
          if (callbacks.onMessage) {
            callbacks.onMessage(event);
          }
        }
      },
      onClose: (event) => {
        // Reset authentication flag
        this.authSent = false;

        // Call the original onClose callback if provided
        if (callbacks.onClose) {
          callbacks.onClose(event);
        }
      },
      onError: (event) => {
        // Call the original onError callback if provided
        if (callbacks.onError) {
          callbacks.onError(event);
        }
      }
    };

    // Call the parent constructor with enhanced options and callbacks
    super({ ...config, options }, enhancedCallbacks);

    // Human handover specific properties
    this.authToken = config.authToken;
    this.isShared = config.isShared;
    this.isInHumanHandoverApp = config.isInHumanHandoverApp;
    this.authSent = false;

    // Typing state management
    this.typingSent = false;

    // Store the specialized callbacks
    this.specializedCallbacks = {
      onAgentJoined: callbacks.onAgentJoined,
      onAgentResolved: callbacks.onAgentResolved,
      onMessageReceived: callbacks.onMessageReceived,
      onTypingStart: callbacks.onTypingStart,
      onTypingStop: callbacks.onTypingStop
    };
  }

  /**
   * Send authentication message
   * @returns {boolean} Whether the authentication message was sent successfully
   */
  sendAuthentication() {
    if (this.authSent || !this.isConnected) {
      return false;
    }

    // Create authentication message
    const authMessage = {
      auth_token: this.authToken,
      shared: this.isShared
    };

    // Send the authentication message
    const success = this.send(JSON.stringify(authMessage));

    if (success) {
      this.authSent = true;
    }

    return success;
  }

  /**
   * Send a user message to the human handover WebSocket
   * @param {string} text - Message text to send
   * @param {boolean} isQuickResponse - Whether this is a quick response
   * @param {Array} images - Array of image URLs to include with the message
   * @param {Object} additionalData - Additional data to include with the message
   * @returns {boolean} Whether the message was sent successfully
   */
  sendUserMessage(text, isQuickResponse = false, images = [], additionalData = {}) {
    // Ensure we're connected and authenticated
    if (!this.isConnected) {
      console.error('Cannot send message: WebSocket is not connected');
      return false;
    }

    if (!this.authSent) {
      console.error('Cannot send message: Not authenticated');
      return false;
    }

    // Create message data
    const messageData = {
      type: 'message',
      data: {
        text,
        is_quick_response: isQuickResponse,
        images: images || [],
        ...additionalData
      }
    };

    // Add agent info if in human handover app
    if (this.isInHumanHandoverApp && additionalData.agent_name) {
      messageData.data.agent_name = additionalData.agent_name;
      messageData.data.agent_profile_pic = additionalData.agent_profile_pic;
    }

    // Send the message
    return this.send(JSON.stringify(messageData));
  }

  /**
   * Send a message to the human handover WebSocket
   * @param {Object} messageData - Message data to send
   * @returns {boolean} Whether the message was sent successfully
   */
  sendMessage(messageData) {
    // Ensure we're connected and authenticated
    if (!this.isConnected) {
      console.error('Cannot send message: WebSocket is not connected');
      return false;
    }

    if (!this.authSent) {
      console.error('Cannot send message: Not authenticated');
      return false;
    }

    // Send the message
    return this.send(JSON.stringify(messageData));
  }

  /**
   * Send a control message like human_handover_taken or human_handover_resolved
   * @param {string} type - The type of control message ('human_handover_taken' or 'human_handover_resolved')
   * @param {string} conversationId - The conversation ID
   * @returns {boolean} Whether the message was sent successfully
   */
  sendControlMessage(type, conversationId, additionalData = {}) {
    // Ensure we're connected and authenticated
    if (!this.isConnected) {
      console.error('Cannot send control message: WebSocket is not connected');
      return false;
    }

    if (!this.authSent) {
      console.error('Cannot send control message: Not authenticated');
      return false;
    }

    // Only allow specific control message types
    if (type !== 'human_handover_taken' && type !== 'human_handover_resolved') {
      console.error(`Invalid control message type: ${type}`);
      return false;
    }

    // Create the message data
    const messageData = {
      type,
      conversation_id: conversationId
    };

    // Add additional data if provided
    if (Object.keys(additionalData).length > 0) {
      messageData.data = additionalData;
    }

    // Send the message
    const success = this.send(JSON.stringify(messageData));

    if (success) {
      console.log(`Sent ${type} message`);
    }

    return success;
  }

  /**
   * Send a typing start event
   * @returns {boolean} Whether the typing event was sent successfully
   */
  sendTypingStart() {
    // Ensure we're connected and authenticated
    if (!this.isConnected) {
      console.error('Cannot send typing event: WebSocket is not connected');
      return false;
    }

    if (!this.authSent) {
      console.error('Cannot send typing event: Not authenticated');
      return false;
    }

    // Only send if not already in typing state
    if (!this.typingSent) {
      const typingData = {
        type: 'typing_start'
      };

      const success = this.send(JSON.stringify(typingData));

      if (success) {
        this.typingSent = true;
      }

      return success;
    }

    return true; // Already in typing state
  }

  /**
   * Send a typing stop event
   * @returns {boolean} Whether the typing event was sent successfully
   */
  sendTypingStop() {
    // Ensure we're connected and authenticated
    if (!this.isConnected) {
      console.error('Cannot send typing event: WebSocket is not connected');
      return false;
    }

    if (!this.authSent) {
      console.error('Cannot send typing event: Not authenticated');
      return false;
    }

    // Only send if currently in typing state
    if (this.typingSent) {
      // Reset typing state
      this.typingSent = false;

      const typingData = {
        type: 'typing_stop'
      };

      const success = this.send(JSON.stringify(typingData));

      return success;
    }

    return true; // Already not typing
  }

  /**
   * Clean up typing state when disconnecting
   */
  disconnect(code, reason) {
    // Reset typing state
    this.typingSent = false;

    // Call parent disconnect
    return super.disconnect(code, reason);
  }
}

export default HumanHandoverWebSocketService;

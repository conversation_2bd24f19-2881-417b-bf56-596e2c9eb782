import generateMarkdownBubble from '@/helpers/generateBubbleMessages';

import DBubbleMessage from '../../Global/DBubbleMessage';
import { DateTime } from 'luxon';
import SourcesPopup from '@/components/SourcesPopup';
import clsx from 'clsx';
import DSuggestionPrompt from '@/components/Global/DSuggestionPrompt';
import { Transition } from '@headlessui/react';
import { CHATBOTS_WITH_NEW_DESIGN } from '@/constants';
import TypingIndicator from '@/components/Global/TypingIndicator';
import { useEffect, useRef } from 'react';

const ChatListMessages = ({
  chatContainerRef,
  messages,
  transformLinkUri,
  readonly = false,
  hideFooter = false,
  chatbot_profile_pic,
  isInApp,
  handleFooterButton,
  isDanteFaq,
  showDate = false,
  sources,
  openSources,
  setOpenSources,
  showSources,
  sourcesLoading,
  chatImageLoader,
  place,
  isInHumanHandoverApp,
  interactingWithLiveAgent,
  pollResponse,
  kb_id,
  showButtons,
  dynamicButtons,
  isLiveAgentAvailable,
  isPreviousConversationHasLiveAgent,
  isAnswerLoading,
  isPreviewMode,
  handleOpenLiveAgent,
  showSuggestionPrompts,
  isLoadingSuggestions,
  handleSuggestionClick,
  config,
  isTyping = false,
}) => {
  const typingIndicatorRef = useRef(null);

  // Scroll to typing indicator when it appears
  useEffect(() => {
    if (isTyping && typingIndicatorRef.current && chatContainerRef?.current) {
      // Small delay to ensure the element is rendered
      setTimeout(() => {
        typingIndicatorRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });
      }, 100);
    }
  }, [isTyping, chatContainerRef]);

  return (
    <div
      ref={chatContainerRef}
      className="flex flex-col w-full grow transition duration-300 no-scrollbar overflow-y-auto"
    >
      {messages?.length > 0 &&
        messages
          .filter((message) => {
            return (
              message.content !== '' ||
              message.status === 'loading' ||
              (message.images && message.images.length > 0)
            );
          })
          .map((message, index) => (
            <DBubbleMessage
              key={message.id || message.id_temp || index}
              id={message.id}
              id_temp={message.id_temp}
              role={message.role}
              reaction={message.reaction}
              type={message.type}
              status={message.status}
              readonly={readonly}
              hideFooter={hideFooter}
              isDanteFaq={isDanteFaq}
              chatbot_profile_pic={chatbot_profile_pic}
              agent_profile_pic={message.agent_profile_pic}
              agent_name={message.agent_name}
              isInApp={isInApp}
              isInHumanHandoverApp={isInHumanHandoverApp}
              was_answered={message.was_answered}
              pollResponse={pollResponse}
              images={message.images || []}
              handleFooterButton={(action) => {
                if (
                  action === 'play_message' &&
                  message.reaction === 'play_message'
                ) {
                  action = 'stop_message';
                }
                const newAction =
                  message.reaction === action ? 'no_reaction' : action;
                handleFooterButton({
                  message_id: message.id,
                  action: newAction,
                });
              }}
              completed_date={
                showDate &&
                DateTime.fromISO(message.date_created).setLocale('en-US')
              }
              sources={sources}
              openSources={openSources}
              setOpenSources={setOpenSources}
              showSources={showSources}
              sourcesLoading={sourcesLoading}
              chatImageLoader={chatImageLoader}
              place={place}
              interactingWithLiveAgent={interactingWithLiveAgent}
              messageIndex={index}
              kb_id={kb_id}
            >
              {message.status !== 'loading' &&
                message.content !== '' &&
                generateMarkdownBubble(transformLinkUri, message, kb_id)}
            </DBubbleMessage>
          ))}

        {/* Typing indicator for human handover */}
        {isTyping && (
          <div ref={typingIndicatorRef} className="flex justify-start mb-2">
            <div className="max-w-[80%]">
              <TypingIndicator
                dotsOnly={true}
              
              />
            </div>
          </div>
        )}


        {((dynamicButtons?.show_calendly_url &&
          dynamicButtons?.show_live_agent) ||
          isLiveAgentAvailable() ||
          (config?.suggested_prompts_enabled &&
            config?.prompt_suggestions?.length > 0 &&
            showSuggestionPrompts)) &&
          showButtons && !isInHumanHandoverApp && CHATBOTS_WITH_NEW_DESIGN.includes(kb_id) && (
            <div className="flex gap-size1 grow-0 flex-wrap justify-end flex-shrink-0 overflow-y-visible suggested-prompts-container animate-fadeInUp max-w-[50%] self-end mb-2">
              {dynamicButtons?.show_calendly_url && !isInApp && (
                <DSuggestionPrompt
                  type="book_meet"
                  disabled={isPreviewMode}
                  onClick={() =>
                    window.open(config?.calendly_url, '_blank')
                  }
                  content={
                    config?.calendly_btn_text || 'Book a meeting'
                  }
                />
              )}
              {(dynamicButtons?.show_live_agent &&
                isLiveAgentAvailable()) && !isPreviousConversationHasLiveAgent && !isInApp && !isAnswerLoading && (
                <DSuggestionPrompt
                  type="connect_live_agent"
                  content={'Connect to Live Agent'}
                  onClick={handleOpenLiveAgent}
                  disabled={isPreviewMode}
                />
              )}
                {config?.suggested_prompts_enabled &&
                  config?.prompt_suggestions?.length > 0 &&
                  showSuggestionPrompts &&
                  config?.prompt_suggestions.map((suggestion, index) => (
                      <Transition
                        key={index}
                        show={showSuggestionPrompts}
                      >
                        <div className="flex-shrink-0">
                          {showSuggestionPrompts && (
                            <div
                              className={clsx(
                                'suggestion-prompt',
                                'h-full',
                                `transition-all duration-200 data-[enter]:delay-${
                                  (index + 1) * 200
                                } data-[closed]:opacity-0 data-[closed]:translate-y-2`,
                                `data-[leave]:delay-${
                                  (config?.prompt_suggestions
                                    ?.length -
                                    index) *
                                  200
                                } data-[leave]:opacity-0`
                              )}
                            >
                              <DSuggestionPrompt
                                content={suggestion.content}
                                onClick={() =>
                                  handleSuggestionClick(suggestion)
                                }
                                disabled={isPreviewMode || isAnswerLoading}
                                kb_id={kb_id}
                              />
                            </div>
                          )}
                        </div>
                      </Transition>
                  )
                )}
            </div>
          )}
        <SourcesPopup
          open={openSources}
          onClose={() => setOpenSources(false)}
          data={sources}
        />
    </div>
  );
};

export default ChatListMessages;

import { useState } from 'react';
import * as authService from '@/services/auth.service';
import validateEmail from '@/helpers/validateEmail';
import { Link, useNavigate } from 'react-router-dom';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';

const ForgotPassword = () => {
  const [emailError, setEmailError] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const validateForm = () => {
    let isValid = true;

    // Email validation
    if (!email || email.trim() === '') {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    } else {
      setEmailError('');
    }

    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      const response = await authService.forgotPassword({
        email: email.trim(),
      });
      if (response.status === 202) {
        setIsSubmitted(true);
      
      }
    } catch (err) {
      console.error(err);
      setError(
        err.response?.data?.detail || 'Failed to send password reset email'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-dvh w-full overflow-hidden">
      {/* <div className="absolute top-[-50%] left-[-20%] w-[120%] h-[120%] rounded-full bg-gradient-radial from-purple-100 to-transparent opacity-20"></div> */}
      <div className="absolute bottom-[-60%] right-[-20%] w-[120%] h-[120%] rounded-full bg-gradient-radial from-purple-200 to-transparent opacity-20"></div>

      {/* Content with entry animation */}
      <div className="relative z-10 w-full max-w-md p-4 animate-fadeIn">
        <div className="flex justify-center mb-4 h-4 md:h-8">
          <DFullLogo />
        </div>
        {/* Wrapper for both main content and signup link with single animation */}
        <div className="animate-fadeInUp">
          <div className="bg-white rounded-t-xl md:rounded-t-3xl shadow-lg p-4 md:p-7 mx-2 md:mx-0">
            {/* Back Button */}
            <div className="mb-6">
              <button
                onClick={() => navigate('/log-in')}
                className="flex items-center text-gray-600 font-medium hover:text-gray-900"
                disabled={loading}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 10H5M5 10L10 15M5 10L10 5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="ml-1">Back to Login</span>
              </button>
            </div>

            {/* Password Reset Icon */}
            <div className="flex justify-center mb-6">
              <div className="relative">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 80 80"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="40" cy="40" r="30" fill="#F0F0FF" />
                  <path
                    d="M32 40H48"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                  />
                  <path
                    d="M40 32V48"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                  />
                  <circle
                    cx="40"
                    cy="40"
                    r="20"
                    stroke="#8070F2"
                    strokeWidth="3"
                  />
                </svg>
              </div>
            </div>

            {/* Header */}
            <div className="text-center mb-7 pt-4">
              <h1 className="text-2xl font-medium mb-2">Reset your password</h1>
              <p className="text-gray-500 text-sm">
                {isSubmitted
                  ? `If an account exists for ${email}, a password reset link has been sent.`
: `Please enter your email address, and we'll send you instructions to reset your password.`}
              </p>
            </div>

            {isSubmitted ? (
              // Success Message
              <div className="mb-6" style={{ minHeight: '180px' }}>
                <div className="mb-4">
                  <div className="text-center text-gray-600">
                    <p className="mt-4">
                      Didn't receive the email? Check your spam folder or try
                      again.
                    </p>
                  </div>
                  <div className="h-5 mt-1"></div>
                </div>

                <button
                  onClick={handleSubmit}
                  className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
                  disabled={loading}
                >
                  {loading ? (
                    <svg
                      className="animate-spin h-5 w-5 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : null}
                  Resend Reset Link
                </button>

                <div className="h-6 mt-3">
                  {error && (
                    <p className="text-red-500 text-sm text-center animate-fadeIn">
                      {error}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              // Email Form
              <div className="mb-6" style={{ minHeight: '180px' }}>
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <input
                      type="email"
                      placeholder="Enter your email address"
                      className={`w-full py-3 px-4 border ${
                        emailError ? 'border-red-500' : 'border-gray-200'
                      } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-min-safe-input text-gray-700`}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={loading}
                    />
                    <div className="h-5 mt-1">
                      {emailError && (
                        <p className="text-red-500 text-xs">{emailError}</p>
                      )}
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
                    disabled={loading}
                  >
                    {loading ? (
                      <svg
                        className="animate-spin h-5 w-5 mr-2"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    ) : null}
                    Reset Password
                  </button>

                  <div className="h-6 mt-3">
                    {error && (
                      <p className="text-red-500 text-sm text-center animate-fadeIn">
                        {error}
                      </p>
                    )}
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* Sign Up Link - always shown */}
          <div className="bg-gray-100 py-4 rounded-b-xl md:rounded-b-3xl text-center shadow-lg mx-2 md:mx-0">
            <p className="text-sm text-gray-500">
              Don't have an account?{' '}
              <Link to="/sign-up" className="text-indigo-600 hover:underline">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;

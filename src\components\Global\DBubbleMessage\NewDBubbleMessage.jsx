import React, { useState } from 'react';
import UserMessage from '@/components/UserMessage';
import UserIcon from '../Icons/NewUserIcon';
import HumanAgent from '@/components/HumanAgent';
import PreviewImageModal from '../PreviewImageModal';
import DNewWritingLoader from '../DNewWritingLoader';

/**
 * NewDBubbleMessage component - A new implementation of the DBubbleMessage component
 * @param {Object} props
 * @param {Object} props.message - The message object
 * @param {string} props.chatbot_profile_pic - URL of the chatbot profile picture
 * @param {string} props.agent_profile_pic - URL of the agent profile picture
 * @param {string} props.agent_name - Name of the agent
 * @param {boolean} props.isInApp - Whether the chat is in the app
 * @param {boolean} props.isDanteFaq - Whether this is the Dante FAQ
 * @param {boolean} props.was_answered - Whether the message was answered
 * @param {Object} props.completed_date - Completed date object
 * @param {Array} props.sources - Sources to show
 * @param {boolean} props.openSources - Whether sources are open
 * @param {Function} props.setOpenSources - Function to set openSources
 * @param {boolean} props.showSources - Whether to show sources
 * @param {boolean} props.sourcesLoading - Whether sources are loading
 * @param {Function} props.handleFooterButton - Function to handle footer button click
 * @param {React.ReactNode} props.children - Child elements
 * @returns {JSX.Element}
 */
const NewDBubbleMessage = ({ 
  message,
  chatbot_profile_pic,
  agent_profile_pic,
  agent_name,
  isInApp = false,
  isDanteFaq = false,
  was_answered,
  type,
  children,
  status,
  interactingWithLiveAgent = false,
  pollResponse,
}) => {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isAgent = message.role === 'agent';
  const [previewImage, setPreviewImage] = useState(null);

  // Check if message has images
  const hasImages = message.images && message.images.length > 0;
  
  // Function to render message images
  const renderImages = (images) => {
    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {images.map((image, index) => (
          <div
            key={index}
            className="relative w-24 h-24 rounded-md overflow-hidden cursor-pointer my-2"
            onClick={() => setPreviewImage(image)}
          >
            <img
              src={image}
              alt={`Image ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>
    );
  };
  
  return (
    <>
      {previewImage && (
        <PreviewImageModal
          isOpen={!!previewImage}
          onClose={() => setPreviewImage(null)}
          image={previewImage}
        />
      )}
      <div className={`flex w-full ${isUser ? 'justify-end' : 'justify-start'}`}>
        {(type === 'info' || type === 'agent_connecting') && <div className='w-full'>
          <p className='text-newGrey text-sm font-medium text-center'>{message.content}</p>  
        </div>}
        {isUser && (type === 'message' || type === 'live_agent_message' || type === 'human_handover_message' || type === 'normal' ) ? (
          <div className="flex flex-col max-w-[80%]" style={{ scrollMarginTop: '-70px' }}>
            {message.content && <UserMessage 
              message={message.content}
              isSent={true}
              className="max-w-[100%]"
              icon={type === 'live_agent_message' ? <UserIcon className='size-4' /> : null}
            />}
            {/* User message images */}
            {hasImages && renderImages(message.images)}
          </div>
        ) : (isAssistant || isAgent) && (type === 'message' || type === 'welcome_message' || type === 'normal') ? (
          <div className="max-w-[80%]">
            {isAgent && (
              <div className="flex items-center gap-1">
                {agent_profile_pic && (
                  <img 
                    src={agent_profile_pic} 
                    alt={agent_name || "Agent"} 
                    className="w-4 h-4 rounded-size1"
                  />
                )}
                <span className="text-sm font-medium text-newGrey">
                  {agent_name || "Agent"}
                </span>
              </div>
            )}
            
            {/* Message content */}
            <div className={status !== 'loading' ? '' : 'hidden'}>
              {children || message.content}
            </div>

            {status === 'loading' && (
              <div
                style={{
                  color: 'var(--dt-color-brand-100)',
                  backgroundColor: 'transparent',
                }}
              >
                <div className="flex items-center justify-start gap-size1 h-6 d-new-writing-loader -ml-size0">
                  <DNewWritingLoader />
                </div>
              </div>
            )}
            
            
            {/* Assistant message images */}
            {hasImages && status !== 'loading' && renderImages(message.images)}
          </div>
        ) : isAssistant && type === 'human_handover_message' ? (
          <HumanAgent 
            agent_name={agent_name}
            agent_profile_pic={agent_profile_pic}
            message={message.content}
            images={message.images}
            setPreviewImage={setPreviewImage}
          />
        ) : null}
      </div>
    </>
  );
};

export default NewDBubbleMessage; 
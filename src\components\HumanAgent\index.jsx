import React from 'react';
import PropTypes from 'prop-types';

/**
 * HumanAgent - A component for displaying human agent messages
 * @param {Object} props - Component props
 * @param {string} props.message - The message content
 * @param {string} props.name - The agent's name
 * @param {string} props.role - The agent's role
 * @param {string} props.className - Additional CSS classes
 */
const HumanAgent = ({
  message,
  agent_name,
  role = "Customer Support Specialist",
  className = '',
  images,
  setPreviewImage,
  ...props
}) => {
  return (
    <div
      className={`
        inline-flex flex-col items-start gap-[2px] px-4 py-2
        font-['Inter',_sans-serif] rounded-2xl border new-border-light
        ${className}
      `}
      {...props}
    >
      {/* Agent info with dot separator */}
      {agent_name && <div className="flex items-center gap-1.5 text-xs font-medium text-newGrey ">
        <span className='text-[12px] leading-[160%]'>{agent_name}</span>
        {role && <div className="w-[3px] h-[3px] rounded-full bg-newGrey leading-[160%]"></div>}
        <span className='text-[12px] leading-[160%]'>{role}</span>
      </div>}
      
      {/* Message content */}
      <div className="text-base font-normal new-text-dark dark:text-white leading-[160%]">
        {message}
      </div>

      {images && <div className="flex flex-wrap gap-2 cursor-pointer" onClick={() => setPreviewImage(images)}>
        {images.map((image, index) => (
          <img key={index} src={image} alt="Agent message" className="w-16 h-16 rounded-md" />
        ))}
      </div>}
    </div>
  );
};

HumanAgent.propTypes = {
  message: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired,
  className: PropTypes.string
};

export default HumanAgent; 
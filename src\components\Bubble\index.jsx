import React, { useEffect, useState, useRef } from 'react';
import Cha<PERSON><PERSON>anager from '../Chatbot/ChatManager';
import DButtonIcon from '../Global/DButtonIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import OptionsIcon from '../Global/Icons/OptionsIcon';
import HomeContent from './HomeContent';
import DashboardIcon from '../Global/Icons/DashboardIcon';
import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import DFullLogo from '../Global/DLogo/DFullLogo';
import ResetIcon from '../Global/Icons/ResetIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import VoiceUI from '../Voice/VoiceUI';
import DChatPassword from '../DChatPassword';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import EmailIcon from '../Global/Icons/EmailIcon';
import <PERSON>hapeLogo from '../Global/DLogo/DShapeLogo';
import DTooltip from '../Global/DTooltip';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import MenuButtonIcon from '../Global/Icons/MenuButtonIcon';
import NewHomeIcon from '../Global/Icons/NewHomeIcon';
import NewChatIcon from '../Global/Icons/NewChatIcon';
import clsx from 'clsx';
import { CHATBOTS_WITH_NEW_DESIGN } from '@/constants';

const Bubble = ({
  config,
  type = 'chatbot',
  initialShouldFetchCustomization,
  isInApp = false,
  isPreviewMode = false,
  isVoiceMode = false,
  slots,
  onNewConversationCreated
}) => {
  const [activeTab, setActiveTab] = useState(config?.initialActiveTab || 'home');
  const [hideLogo, setHideLogo] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(!config?.public);
  const [cookie, setCookie] = useState(null); //this is used on bubble when password is enabled
  const bubbleRef = useRef(null);
  const [showEmailTranscript, setShowEmailTranscript] = useState(false);
  const [logoOpacity, setLogoOpacity] = useState(1);
  const [promptToSend, setPromptToSend] = useState(null);
  const [orderedTabs, setOrderedTabs] = useState([]);

  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    config?.onTabChange?.(tab);
  };

  const handleScroll = () => {
    if (bubbleRef.current) {
      const scrollTop = bubbleRef.current.scrollTop;
      const newOpacity = Math.max(1 - scrollTop / 70, 0);
      setLogoOpacity(newOpacity);
    }
  };

  const resetChat = () => {
    resetCurrentConversation();
  };
  const handleCloseClick = () => {
    const message = { eventType: 'chatbotCloseClick', eventData: true };
    if (config.custom_url) {
      window.parent.parent.postMessage(message, '*');
    } else {
      window.parent.postMessage(message, '*');
    }
  };

  useEffect(() => {
    setShowPasswordModal(!config?.public);
  }, [config?.public]);

  useEffect(() => {
    const currentRef = bubbleRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (currentRef) currentRef.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const message = { eventType: 'siteReady' };

    // if (window.parent !== window.top) {
    //   window.top.postMessage(message, '*');
    // } else {
    window.parent.postMessage(message, '*');
    // }
  }, []);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.eventType === 'promptClick') {
        setPromptToSend(event.data.eventData);
        setActiveTab('chat');
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    if (config?.initialActiveTab && config.initialActiveTab !== activeTab) {
      setActiveTab(config.initialActiveTab);
    }
  }, [config?.initialActiveTab]);

  const [parentWindowWidth, setParentWindowWidth] = useState(window.innerWidth);

  // Request parent window width via postMessage
  useEffect(() => {
    const requestParentWidth = () => {
      try {
        // Try direct access first (same-origin case)
        setParentWindowWidth(window.parent.innerWidth);
      } catch (error) {
        // Cross-origin case: request width via postMessage
        window.parent.postMessage({ eventType: 'requestParentWidth' }, '*');
      }
    };

    requestParentWidth();

    // Listen for response from parent
    const handleMessage = (event) => {
      if (event.data.eventType === 'parentWidthResponse') {
        setParentWindowWidth(event.data.width);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  console.log('active tab', parentWindowWidth);

  useEffect(() => {
    const tabs = [
      {
        id: 'home',
        order: config?.main_tabs?.home?.order || 1,
        label: config?.main_tabs?.home?.label || 'Home',
        icon: CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? <NewHomeIcon className='size-5'/> : <DashboardIcon className="font-bold" />,
        onClick: () => {
          handleTabChange('home');
        },
      },
      {
        id: 'chat',
        order: config?.main_tabs?.chat?.order || 2,
        label: config?.main_tabs?.chat?.label || 'Chat',
        icon: CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? <NewChatIcon className='size-5'/> : <AiChatbotIcon className="font-bold" />,
        onClick: () => handleTabChange('chat'),
      },
    ];

    const sorted = [...tabs].sort((a, b) => a.order - b.order);
    setOrderedTabs(sorted);

    // If no initialActiveTab is set, use the first tab from the sorted array
    if (!config?.initialActiveTab && sorted.length > 0) {
      setActiveTab(sorted[0].id);
    }
  }, [config?.main_tabs, config?.initialActiveTab]);

  

  return !isVoiceMode ? (
    <>
      <div
        className="flex flex-col rounded-size1 h-full grow bubble  no-scrollbar relative overflow-hidden"
        style={{
          background:
            activeTab === 'home' && config?.home_tab_enabled
              ? 'radial-gradient(243.51% 165.3% at -5% -5%, var(--dt-color-brand-60) 0%, var(--dt-color-surface-100) 40%)'
              : 'var(--dt-color-surface-100)',
          // borderColor: `var(--dt-color-element-5)`,
          // borderWidth: '1px',
        }}
      >
        <header className={clsx("p-size3 absolute top-0 left-0 right-0", CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && 'border-b border-grey-5')}>
          {activeTab === 'home' && config?.home_tab_enabled ? (
            <div className="flex items-center justify-between w-full">
              {!hideLogo && config?.embed_logo_url ? (
                <img
                  src={
                    config?.embed_logo_url instanceof File
                      ? URL.createObjectURL(config?.embed_logo_url)
                      : config?.embed_logo_url
                  }
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                  className="w-auto h-[28px] "
                />
              ) : (
                <div
                  className="!w-auto  flex items-center py-2"
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                >
                  <DFullLogo />
                </div>
              )}
              {((isPreviewMode || isInApp) && !CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id)) ? (
                <DButtonIcon
                  variant="squared"
                  className="size-8 backdrop-blur-lg ml-auto !z-10"
                  style={{
                    color: 'var(--dt-color-element-100)', 
                    borderColor: 'var(--dt-color-element-2)',
                    backgroundColor: 'var(--dt-color-element-2)',
                  }}
                  onClick={handleCloseClick}
                >
                  <DTooltip content="Close icon works on shared chat bubble" position="left bottom">
                    <CloseIcon />
                  </DTooltip>
                </DButtonIcon>
              ) : (
                parentWindowWidth < 768 && CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && (
                  <DButtonIcon
                    variant="squared"
                    className="size-8 backdrop-blur-lg ml-auto !z-10 a"
                    style={{
                      color: 'var(--dt-color-element-100)',
                      borderColor: 'var(--dt-color-element-2)', 
                      backgroundColor: 'var(--dt-color-element-2)',
                    }}
                    onClick={handleCloseClick}
                  >
                    <CloseIcon />
                  </DButtonIcon>
                )
              )}
            </div>
          ) : (
            <div className="flex items-center justify-between w-full">
              {/* <DButtonIcon
                variant="squared"
                style={{
                  color: 'var(--dt-color-element-100)',
                  borderColor: 'var(--dt-color-element-5)',
                  backgroundColor: 'var(--dt-color-surface-100)',
                }}
                className={`px-size0 !z-10 ${
                  !config?.home_tab_enabled ? 'hidden' : ''
                }`}
                disabled={isPreviewMode}
                onClick={() => {
                  handleTabChange('home');
                }}
              >
                <ChevronLeftIcon />
              </DButtonIcon> */}
              <div className="flex items-center gap-1 min-w-0 flex-1 mr-4">
              {config?.chatbot_icon ? (
                <img
                  src={
                    config?.chatbot_icon instanceof File
                      ? URL.createObjectURL(config?.chatbot_icon)
                      : config?.chatbot_icon
                  }
                  style={{
                    opacity: logoOpacity,
                    transition: 'opacity 0.3s ease',
                  }}
                  className="w-auto h-[28px]  flex-shrink-0"
                />):
                  <DButtonIcon className="!w-[20px] flex-shrink-0">
                    <DShapeLogo className="!size-5"/>
                  </DButtonIcon>
                }
                {config?.show_chatbot_name  && (
                  <h2
                    className="text-base"
                    style={{
                      fontFamily: 'var(--dt-font-family), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
                      color: 'var(--dt-color-element-100)',
                    }}
                  >
                    {config?.name || config?.kb_name}
                  </h2>
                )}
              </div>
              <div className="flex items-center gap-size2 flex-shrink-0">
                <Menu>
                  <MenuButton
                    variant="squared"
                    style={{
                      color: 'var(--dt-color-element-100)',
                      borderColor: 'var(--dt-color-element-5)',
                      backgroundColor: 'var(--dt-color-surface-100)',
                    }}
                    className="px-size0 !z-10"
                    disabled={isPreviewMode}
                  >
                    {CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) ? <MenuButtonIcon className="hover:text-accent-hover focus:text-accent" /> :  <OptionsIcon />}
                  </MenuButton>
                  <MenuItems
                    anchor="bottom right"
                    className="mt-2 w-56 origin-top-right rounded-size1 bg-[var(--dt-color-surface-100)] shadow-lg border border-grey-5 z-20"
                  >
                    {config.show_email_details && (
                      <MenuItem>
                        {({ active }) => (
                          <button
                            className={`email-transcript-button ${
                              active ? 'bg-[var(--dt-color-element-5)]' : ''
                            } flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 w-full border-b border-grey-5`}
                            onClick={() => setShowEmailTranscript(true)}
                          >
                            <p className="text-base font-regular tracking-tight">
                              Email transcript
                            </p>
                            <EmailIcon />
                          </button>
                        )}
                      </MenuItem>
                    )}
                    <MenuItem>
                      {({ active }) => (
                        <button
                          className={`reset-chatbot-button ${
                            active ? 'bg-[var(--dt-color-element-5)]' : ''
                          } flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 w-full border-b border-grey-5`}
                          onClick={resetChat}
                        >
                          <p className="text-base font-regular tracking-tight">Reset chat</p>
                          <ResetIcon />
                        </button>
                      )}
                    </MenuItem>
                    <MenuItem>
                      {({ active }) => (
                        <button
                          className={`terms-of-use-button ${
                            active ? 'bg-[var(--dt-color-element-5)]' : ''
                          } flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 w-full border-b border-grey-5`}
                          onClick={() =>
                            window.open(
                              config?.terms_of_use_link ||
                                'https://www.dante-ai.com/terms-of-service',
                              '_blank'
                            )
                          }
                        >
                          <p className="text-base font-regular tracking-tight">
                            Terms of use
                          </p>
                          <UpRightIcon />
                        </button>
                      )}
                    </MenuItem>
                    <MenuItem>
                      {({ active }) => (
                        <button
                          className={`privacy-policy-button ${
                            active ? 'bg-[var(--dt-color-element-5)]' : ''
                          } flex justify-between text-[var(--dt-color-element-100)] items-center gap-size1 py-size1 px-size2 rounded-size1 w-full`}
                          onClick={() =>
                            window.open(
                              config?.privacy_policy_link ||
                                'https://www.dante-ai.com/privacy-policy',
                              '_blank'
                            )
                          }
                        >
                          <p className="text-base font-regular tracking-tight">
                            Privacy policy
                          </p>
                          <UpRightIcon />
                        </button>
                      )}
                    </MenuItem>
                  </MenuItems>
                </Menu>
                {(!isInApp && (!CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) || parentWindowWidth <= 768)) && <DButtonIcon
                  variant="squared"
                  className="size-8 backdrop-blur-lg !z-10"
                  disabled={isPreviewMode}
                  style={{
                    color: 'var(--dt-color-element-100)',
                    borderColor: 'var(--dt-color-element-2)',
                    backgroundColor: 'var(--dt-color-element-2)',
                  }}
                  onClick={handleCloseClick}
                >
                  <CloseIcon />
                </DButtonIcon>}
              </div>
            </div>
          )}
        </header>

        <div
          className={clsx("bubble-body flex-grow p-4 overflow-auto pt-[64px] no-scrollbar", CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && '!pb-0 !px-0')}
          ref={bubbleRef}
        >
          {activeTab === 'home' && config?.home_tab_enabled ? (
            <HomeContent
              kb_id={config?.kb_id}
              token={config?.token}
              slots={slots}
              isPreviewMode={isPreviewMode}
              setActiveTab={handleTabChange}
              hidePoweredByDante={config?.remove_watermark}
              previous_conversation_enabled={
                config?.previous_conversation_enabled
              }
            />
          ) : (
            (activeTab === 'chat' || !config?.home_tab_enabled) && type === 'chatbot' && (
              <ChatManager
                config={config}
                isInApp={isInApp}
                initialShouldFetchCustomization={
                  initialShouldFetchCustomization
                }
                isPreviewMode={isPreviewMode}
                cookie={cookie}
                setCookie={setCookie}
                showInAppHeader={false}
                promptToSend={promptToSend}
                onPromptProcessed={() => setPromptToSend(null)}
                setShowEmailTranscript={setShowEmailTranscript}
                showEmailTranscript={showEmailTranscript}
                onNewConversationCreated={onNewConversationCreated}
              />
            )
          )}
        </div>
        {config?.home_tab_enabled && !CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && (
          <footer
            className="flex-none flex border-t border-grey-5"
            style={{
              background: `linear-gradient(0deg, var(--dt-color-surface-100), var(--dt-color-surface-100)),
                 linear-gradient(0deg, rgba(9, 8, 31, 0.01), rgba(9, 8, 31, 0.01))`,
            }}
          >
            {orderedTabs.map((tab) => (
              <button
                key={tab.id}
                className={`flex-1 py-3 text-center text-sm font-regular flex flex-col items-center justify-center gap-size0 ${
                  activeTab === tab.id
                    ? 'text-[var(--dt-color-element-100)]'
                    : 'text-[var(--dt-color-element-50)]'
                }`}
                onClick={tab.onClick}
              >
                {/* {tab.icon} */}
                {tab.label}
              </button>
            ))}
          </footer>
        )}
        {
          CHATBOTS_WITH_NEW_DESIGN.includes(config?.kb_id) && config?.home_tab_enabled && (
            <footer className="flex-none flex h-[64px]">
              {orderedTabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`flex-1 text-center text-base font-regular flex items-center justify-center gap-size0 py-size2 px-[10px] hover:text-black ${
                    activeTab === tab.id
                      ? 'text-black'
                      : 'text-newGrey'
                  }`}
                  onClick={tab.onClick}
                >
                  {tab.icon}
                  <p className='text-base font-medium'>{tab.label}</p>
                </button>
            ))}
            </footer>
        )}
        {!config?.public && showPasswordModal && (
          <div className="absolute bottom-0 left-0 right-0 h-full flex flex-col gap-size1 backdrop-blur-sm justify-end">
            <div className="bg-white p-size5 rounded-size1 flex flex-col gap-size1 z-1">
              <DChatPassword
                kb_id={config?.kb_id}
                setCookie={setCookie}
                onUnlockSuccess={() => setShowPasswordModal(false)}
                onBubble={true}
              />
            </div>
          </div>
        )}
      </div>
    </>
  ) : (
    <VoiceUI />
  );
};

export default Bubble;

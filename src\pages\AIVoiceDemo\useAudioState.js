import { useState, useEffect, useRef } from 'react';
import * as protobuf from 'protobufjs';
import { DemoWebSocketHandler, WebSocketHandler } from './WebSocketHandler';
import { AudioHandler } from './AudioHandler';
import { visualize } from './VisualizationUtils';
import { defaultSettings, voiceOptions as staticVoiceOptions, kbOptions as staticKbOptions } from './config';
import { WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';
import * as userService from '@/services/user.service';
import { useUserStore } from '@/stores/user/userStore';

export const useAudioState = () => {
  const [progressText, setProgressText] = useState('Loading, wait...');
  const [isStartButtonDisabled, setIsStartButtonDisabled] = useState(true);
  const [isStopButtonDisabled, setIsStopButtonDisabled] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [userAudioLevel, setUserAudioLevel] = useState(0);
  const [botAudioLevel, setBotAudioLevel] = useState(0);
  const [speaking, setSpeaking] = useState(null); // null, 'user', or 'bot'
  const [smoothedBotLevel, setSmoothedBotLevel] = useState(0);
  const [displayStatus, setDisplayStatus] = useState('Standby');
  const [isLoading, setIsLoading] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [errorDisplayed, setErrorDisplayed] = useState(false); // Flag to track if an error has been displayed

  // Calling state management - single source of truth
  const [callState, setCallState] = useState('idle'); // 'idle', 'calling', 'connected'

  // Initialize settings with static defaults
  const [settings, setSettings] = useState(() => {
    return {
      ...defaultSettings,
      // Use static configs as fallback
      voiceOptions: staticVoiceOptions || { openai: [], cartesia: [], elevenlabs: [] },
      kbOptions: staticKbOptions || []
    };
  });

  // Audio devices state
  const [audioDevices, setAudioDevices] = useState({
    inputDevices: [],
    outputDevices: [],
    selectedInput: 'default',
    selectedOutput: 'default'
  });

  // Refs
  const callingAudioRef = useRef(null);
  const audioContextRef = useRef(null);
  const playTimeRef = useRef(0);
  const lastMessageTimeRef = useRef(0);
  const frameRef = useRef(null);
  const analyserRef = useRef(null);
  const botAnalyserRef = useRef(null);
  const animationFrameRef = useRef(null);
  const statusTimeoutRef = useRef(null);
  const webSocketHandlerRef = useRef(null);
  const audioHandlerRef = useRef(null);
  const outputDeviceIdRef = useRef(null);

  // Initialize calling sound
  useEffect(() => {
    const audio = new Audio('https://dante-ai-voice.lon1.cdn.digitaloceanspaces.com/calling-sound.mp3');
    audio.loop = true;
    audio.preload = 'auto';
    audio.volume = 1.0;

    audio.addEventListener('error', (e) => {
      console.error('Error loading calling sound:', e);
    });

    callingAudioRef.current = audio;

    return () => {
      if (callingAudioRef.current) {
        callingAudioRef.current.pause();
        callingAudioRef.current.src = '';
      }
    };
  }, []);

  // Control calling sound based on callState - with additional safety checks
  useEffect(() => {
    if (callState === 'calling' && callingAudioRef.current) {
      callingAudioRef.current.currentTime = 0;
      callingAudioRef.current.play().catch(err => {
        console.error('Failed to play calling sound:', err);
      });
    } else if (callState !== 'calling' && callingAudioRef.current) {
      callingAudioRef.current.pause();
      callingAudioRef.current.currentTime = 0;
    }
  }, [callState]);

  // Reset error displayed flag when starting a new session
  useEffect(() => {
    if (callState === 'calling') {
      setErrorDisplayed(false);
    }
  }, [callState]);

  // Handle WebSocket messages
  const handleWebSocketMessage = (event) => {
    const arrayBuffer = event.data;

    // First message from server means we're connected
    if (callState === 'calling') {
      setCallState('connected');
    }

    // Process the audio data
    if (audioHandlerRef.current) {
      try {
        // Process audio and get success status
        const success = audioHandlerRef.current.enqueueAudioFromProto(arrayBuffer);

        // If we successfully processed audio data, ensure we're in connected state
        if (success && callState === 'calling') {
          setCallState('connected');
        }
      } catch (error) {
        console.error('Error processing audio data:', error);
      }
    }
  };

  // Handle WebSocket open event
  const handleWebSocketOpen = (analyser) => {
    analyserRef.current = analyser;

    // Start visualization loop
    const visualizeCallback = () => {
      visualize(
        analyserRef,
        botAnalyserRef,
        speaking,
        setUserAudioLevel,
        setSmoothedBotLevel,
        animationFrameRef,
        visualizeCallback
      );
    };

    visualizeCallback();
  };

  // Display error message only if one hasn't been shown yet
  const displayErrorMessage = (message) => {
    if (!errorDisplayed) {
      setErrorDisplayed(true);

      // Use connection status handler if available
      if (window.__voicePreviewConnectionHandler) {
        window.__voicePreviewConnectionHandler('error', message);
      } else {
        setProgressText(message);
      }
    }
  };

  // Handle WebSocket connection errors
  const handleWebSocketError = (errorInfo) => {
    console.error('WebSocket error:', errorInfo);

    // Reset call state
    setCallState('idle');

    // Stop animations
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Update UI
    setIsPlaying(false);
    setIsStartButtonDisabled(false);
    setIsStopButtonDisabled(true);
    setSpeaking(null);

    // Apply explicit UI state overrides if provided in error info
    if (errorInfo.disableStopButton === true) {
      setIsStopButtonDisabled(true);
    }
    if (errorInfo.enableStartButton === true) {
      setIsStartButtonDisabled(false);
    }

    // Handle different error types
    let errorMessage = errorInfo.message || 'WebSocket connection error';

    if (errorInfo.type === 'microphone') {
      // Handle microphone permission errors
      errorMessage = errorInfo.error || 'Microphone access denied';

      // If this is a browser permission error, show a clear message
      if (errorInfo.originalError &&
          (errorInfo.originalError.name === 'NotAllowedError' ||
           errorInfo.originalError.name === 'PermissionDeniedError')) {
        errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings and try again.';
      }
    } else if (errorInfo.serverDown) {
      errorMessage = 'Could not connect to voice server. Please check your internet connection.';
    }

    // Display error message only if one hasn't been shown yet
    displayErrorMessage(errorMessage);
  };

  // Handle WebSocket connection closure
  const handleWebSocketClose = (event) => {
    // Reset call state
    setCallState('idle');

    // Stop animations
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Update UI
    setIsPlaying(false);
    setIsStartButtonDisabled(false);
    setIsStopButtonDisabled(true);
    setSpeaking(null);

    // Display message if unexpected closure and we were playing
    if (isPlaying) {
      const closeMessage = `Connection closed: ${event.reason || 'Unknown reason'}`;

      // Display error message only if one hasn't been shown yet
      displayErrorMessage(closeMessage);
    }
  };

  // Initialize WebSocket with audio constraints
  const initWebSocketWithAudioConstraints = (audioConstraints) => {
    const endpointType = settings.endpointType || WS_ENDPOINT_TYPES.PREVIEW;

    if(endpointType === WS_ENDPOINT_TYPES.DEMO) {
      webSocketHandlerRef.current = new DemoWebSocketHandler({
        onMessage: handleWebSocketMessage,
        onOpen: handleWebSocketOpen,
        onClose: handleWebSocketClose,
        onError: handleWebSocketError,
        frameRef,
        audioContextRef,
        setUserAudioLevel,
        setSpeaking,
        settings,
        audioConstraints,
        endpointType
      });
    }else{
      webSocketHandlerRef.current = new WebSocketHandler({
        onMessage: handleWebSocketMessage,
        onOpen: handleWebSocketOpen,
        onClose: handleWebSocketClose,
        onError: handleWebSocketError,
        frameRef,
        audioContextRef,
        setUserAudioLevel,
        setSpeaking,
        settings,
        audioConstraints,
        endpointType
      });
    }

    webSocketHandlerRef.current.initWebSocket();
  };

  // Start audio session with more robust state management
  const startAudio = async () => {
    if (isStartButtonDisabled) {
      return false;
    }

    // Reset error displayed flag when starting a new session
    setErrorDisplayed(false);

    try {
      // First check for microphone permissions before proceeding
      try {
        // First attempt to quickly check microphone permissions
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // If we get here, we have permissions, so clean up this test stream
        stream.getTracks().forEach(track => track.stop());
      } catch (permError) {
        // Permission denied or device not available
        console.error('Microphone permission denied:', permError);

        // Update UI - this is important to reflect the permission error before WebSocket even starts
        setIsPlaying(false);
        setIsStartButtonDisabled(false);
        setIsStopButtonDisabled(true);

        // Set specific error message based on error type
        let errorMessage = 'Microphone access denied';
        let shouldRetry = false;

        if (permError.name === 'NotAllowedError' || permError.name === 'PermissionDeniedError') {
          errorMessage = 'Microphone access is required for voice chat. Please click "Allow" when prompted and try again.';
          shouldRetry = true;
        } else if (permError.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (permError.name === 'NotReadableError' || permError.name === 'TrackStartError') {
          errorMessage = 'Microphone is in use by another application. Please close other applications using the microphone.';
        }

        // If this is a permission error, try to re-request permission
        if (shouldRetry) {
          try {
            // Show a user-friendly message and re-request permission
            const confirmRetry = window.confirm(
              'Microphone access is required for voice chat.\n\n' +
              'Please click "Allow" when your browser asks for microphone permission.\n\n' +
              'Click OK to try again, or Cancel to continue without voice.'
            );

            if (confirmRetry) {
              // Re-request microphone permission
              const retryStream = await navigator.mediaDevices.getUserMedia({ audio: true });
              retryStream.getTracks().forEach(track => track.stop());
              // If we get here, permission was granted, continue with the function
            } else {
              // User cancelled, show error and return
              displayErrorMessage('Voice chat requires microphone access. Please enable it in your browser settings to use this feature.');
              return false;
            }
          } catch (retryError) {
            // Still denied after retry
            console.error('Microphone permission still denied after retry:', retryError);
            displayErrorMessage('Microphone access is still denied. Please enable microphone access in your browser settings and refresh the page.');
            return false;
          }
        } else {
          // Display error message for non-permission errors
          displayErrorMessage(errorMessage);
          return false;
        }
      }

      // First explicitly set call state to idle (to ensure any previous state is cleared)
      setCallState('idle');

      // Clean up any existing resources
      if (webSocketHandlerRef.current) {
        webSocketHandlerRef.current.cleanup();
        webSocketHandlerRef.current = null;
      }

      if (audioHandlerRef.current) {
        audioHandlerRef.current.cleanup();
        audioHandlerRef.current = null;
      }

      // Force stop any existing calling sound
      if (callingAudioRef.current) {
        callingAudioRef.current.pause();
        callingAudioRef.current.currentTime = 0;
      }

      // Update UI state
      setIsStartButtonDisabled(true);
      setIsStopButtonDisabled(false);
      setIsPlaying(true);
      setSpeaking(null);
      setUserAudioLevel(0);
      setBotAudioLevel(0);
      setSmoothedBotLevel(0);
      setDisplayStatus('Connecting...');

      // Create/resume audio context
      if (!audioContextRef.current) {
        const success = initAudioContext();
        if (!success) {
          throw new Error('Failed to initialize audio context');
        }
      } else if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      // Configure audio constraints
      const selectedInputDevice = audioDevices.selectedInput;
      const audioConstraints = selectedInputDevice !== 'default'
        ? { deviceId: { exact: selectedInputDevice } }
        : true;

      // Store selected output device
      if (audioDevices.selectedOutput !== 'default') {
        outputDeviceIdRef.current = audioDevices.selectedOutput;
      }

      // Reset time references
      playTimeRef.current = 0;
      lastMessageTimeRef.current = 0;

      // Initialize audio components
      audioHandlerRef.current = new AudioHandler({
        audioContextRef,
        playTimeRef,
        lastMessageTimeRef,
        frameRef,
        setSpeaking: (state) => {
          setSpeaking(state);
          // When bot starts speaking, ensure we're in connected state
          if (state === 'bot' && callState !== 'connected') {
            setCallState('connected');
          }
        },
        setSmoothedBotLevel,
        outputDeviceIdRef
      });

      // Store bot analyzer reference
      botAnalyserRef.current = audioHandlerRef.current.getBotAnalyser();

      // NOW set call state to calling - this will trigger the calling sound
      // Do this AFTER all setup is complete
      setCallState('calling');

      // Initialize WebSocket
      initWebSocketWithAudioConstraints(audioConstraints);
      return true;
    } catch (error) {
      console.error('Error starting audio session:', error);

      // Display error message
      displayErrorMessage(error.message || 'Failed to start audio session');

      stopAudio();
      return false;
    }
  };

  // Stop audio session with more robust cleanup
  const stopAudio = () => {
    // First reset call state to explicitly stop the calling sound
    setCallState('idle');

    // Force stop any calling sound directly
    if (callingAudioRef.current) {
      callingAudioRef.current.pause();
      callingAudioRef.current.currentTime = 0;
    }

    // Stop animations
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Clean up WebSocket
    if (webSocketHandlerRef.current) {
      webSocketHandlerRef.current.cleanup();
      webSocketHandlerRef.current = null;
    }

    // Clean up Audio handler
    if (audioHandlerRef.current) {
      audioHandlerRef.current.cleanup();
      audioHandlerRef.current = null;
    }

    // Update UI state
    setDisplayStatus('Standby');
    setSpeaking(null);
    setIsPlaying(false);
    setUserAudioLevel(0);
    setBotAudioLevel(0);
    setSmoothedBotLevel(0);
    setIsStartButtonDisabled(false);
    setIsStopButtonDisabled(true);

    // Call getUserCredits to refresh user's credits after using the voice preview
    if (settings.endpointType === WS_ENDPOINT_TYPES.PREVIEW) {
      try {
        // Get the setCredits function from the user store
        const setCredits = useUserStore.getState().setCredits;

        // Call getUserCredits and update the store with the response
        userService.getUserCredits()
          .then(response => {
            if (response && response.status === 200 && response.data) {
              // Update credits in the user store
              setCredits(response.data);
            }
          })
          .catch(error => {
            console.error('Error fetching updated user credits:', error);
          });
      } catch (error) {
        console.error('Error updating user credits:', error);
      }
    }

    // Final verification that calling sound is stopped after a slight delay
    setTimeout(() => {
      if (callingAudioRef.current) {
        callingAudioRef.current.pause();
        callingAudioRef.current.currentTime = 0;
      }
    }, 100);
  };

  // Enumerate available audio devices
  const enumerateAudioDevices = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
      console.error('enumerateDevices() not supported in this browser');
      return;
    }

    try {
      // We need to request permission first
      await navigator.mediaDevices.getUserMedia({ audio: true });

      const devices = await navigator.mediaDevices.enumerateDevices();

      const inputDevices = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 5)}...`
        }));

      const outputDevices = devices
        .filter(device => device.kind === 'audiooutput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Speaker ${device.deviceId.slice(0, 5)}...`
        }));

      setAudioDevices(prev => ({
        ...prev,
        inputDevices,
        outputDevices
      }));

    } catch (err) {
      console.error('Error enumerating audio devices:', err);
    }
  };

  // Initialize audio context in a way that's optimal for audio quality
  const initAudioContext = () => {
    try {
      // Using settings that match server expectations
      const contextOptions = {
        latencyHint: 'interactive', // Prioritize low latency
        sampleRate: 16000, // Must match SAMPLE_RATE in AudioUtils.js
      };

      // Create new audio context with optimal settings
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)(contextOptions);

      return true;
    } catch (error) {
      console.error('Failed to create audio context:', error);
      displayErrorMessage('Error: Could not initialize audio system. Please check your browser settings.');
      return false;
    }
  };

  // Set selected audio devices with better error handling
  const setAudioDevice = (type, deviceId) => {
    if (!deviceId) {
      console.warn(`Invalid ${type} device ID provided`);
      return false;
    }

    // Update state for which device is selected
    setAudioDevices(prev => ({
      ...prev,
      [type === 'input' ? 'selectedInput' : 'selectedOutput']: deviceId
    }));

    // If we're changing output device and have an active audio context
    if (type === 'output' && audioContextRef.current) {
      // Store the selected output device ID for future audio playback
      outputDeviceIdRef.current = deviceId;

      // If we have an active AudioHandler, inform it of the change
      if (audioHandlerRef.current) {
        // This will be picked up on next audio packet
        return true;
      }
    }

    // For input device changes, we'll need to restart the audio session
    if (type === 'input' && isPlaying) {
      // Restart audio with new input device
      stopAudio();
      setTimeout(() => startAudio(), 500);
    }

    return true;
  };

  // Toggle mute/unmute for the microphone
  const toggleMute = () => {
    if (!webSocketHandlerRef.current || !webSocketHandlerRef.current.microphoneStreamRef) {
      return; // No active microphone stream
    }

    const micTracks = webSocketHandlerRef.current.microphoneStreamRef.getAudioTracks();

    // Toggle mute state for all audio tracks
    micTracks.forEach(track => {
      track.enabled = !track.enabled;
    });

    // Update mute state
    setIsMuted(!isMuted);
  };

  // Load audio devices on component mount
  useEffect(() => {
    enumerateAudioDevices();

    // Re-enumerate when devices change
    navigator.mediaDevices?.addEventListener('devicechange', enumerateAudioDevices);

    return () => {
      navigator.mediaDevices?.removeEventListener('devicechange', enumerateAudioDevices);
    };
  }, []);

  // Load protobuf definition
  useEffect(() => {
    const loadProtobuf = async () => {
      try {
        // Use the correct path to the proto file
        const root = await protobuf.load('/frames.proto');

        if (!root) {
          throw new Error('Failed to load protobuf root');
        }

        const frameType = root.lookupType('pipecat.Frame');
        if (!frameType) {
          throw new Error('Failed to find "pipecat.Frame" type in protobuf definition');
        }

        frameRef.current = frameType;
        setProgressText('We are ready! Configure your voice settings and click `Start Audio`.');
        setIsLoading(false);
        setIsStartButtonDisabled(false);
      } catch (err) {
        console.error('Error loading protobuf:', err);
        let errorMessage = `Error loading protobuf definition: ${err.message}.`;

        if (err.message.includes('not found') || err.message.includes('no such file')) {
          errorMessage += ' The frames.proto file is missing or not accessible.';
        } else if (err.message.includes('parse')) {
          errorMessage += ' The frames.proto file contains syntax errors.';
        } else {
          errorMessage += ' Make sure frames.proto exists in the correct location.';
        }

        displayErrorMessage(errorMessage);
        setIsLoading(false);
        setIsStartButtonDisabled(true); // Keep Start button disabled on error
      }
    };

    loadProtobuf();

    return () => {
      // Component cleanup
      stopAudio();
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close().catch(err => {});
      }
    };
  }, []);

  // Effect to handle status text changes with debouncing
  useEffect(() => {
    // Clear any existing timeout
    if (statusTimeoutRef.current) {
      clearTimeout(statusTimeoutRef.current);
    }

    // Determine the new status
    let newStatus = 'Connected';
    if (!isPlaying) {
      newStatus = 'Standby';
    } else if (isMuted) {
      newStatus = 'Microphone muted';
    } else if (speaking === 'user' && userAudioLevel > 0.1) {
      newStatus = 'Listening...';
    } else if (speaking === 'bot') {
      newStatus = 'Speaking...';
    } else if (callState === 'calling') {
      newStatus = 'Calling...';
    }

    // Only update after a delay to prevent flickering
    statusTimeoutRef.current = setTimeout(() => {
      setDisplayStatus(newStatus);
    }, 300);

    return () => {
      if (statusTimeoutRef.current) {
        clearTimeout(statusTimeoutRef.current);
      }
    };
  }, [isPlaying, speaking, userAudioLevel, botAudioLevel, isMuted, callState]);

  // Update handler with current settings when settings change
  useEffect(() => {
    if (webSocketHandlerRef.current) {
      // Update settings in the handler to ensure it uses the current values
      webSocketHandlerRef.current.settings = settings;
    }
  }, [settings]);

  // Return values and functions
  return {
    progressText,
    isStartButtonDisabled,
    isStopButtonDisabled,
    isPlaying,
    userAudioLevel,
    botAudioLevel,
    speaking,
    smoothedBotLevel,
    displayStatus,
    startAudio,
    stopAudio,
    settings,
    setSettings,
    isLoading,
    audioDevices,
    setAudioDevice,
    isMuted,
    toggleMute,
    hasReceivedFirstMessage: callState === 'connected' // For backward compatibility
  };
};
